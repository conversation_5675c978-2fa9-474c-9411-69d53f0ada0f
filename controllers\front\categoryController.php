<?php
/**
 * ST Smart Blog - Category Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/../../classes/StBlogPost.php';
require_once dirname(__FILE__) . '/../../classes/StBlogCategory.php';

class StSmartBlogCategoryModuleFrontController extends ModuleFrontController
{
    public $ssl = true;
    public $category;

    public function __construct()
    {
        parent::__construct();
        $this->context = Context::getContext();
    }

    public function init()
    {
        parent::init();

        $id_category = (int)Tools::getValue('id_category');
        $category_rewrite = Tools::getValue('category_rewrite');
        $id_lang = $this->context->language->id;
        $id_shop = $this->context->shop->id;

        // Get category by ID or rewrite
        if ($id_category) {
            $this->category = new StBlogCategory($id_category, $id_lang, $id_shop);
        } elseif ($category_rewrite) {
            $this->category = StBlogCategory::getCategoryByRewrite($category_rewrite, $id_lang, $id_shop);
        }

        // Check if category exists and is active
        if (!Validate::isLoadedObject($this->category) || !$this->category->active) {
            Tools::redirect('index.php?controller=404');
        }
    }

    public function initContent()
    {
        parent::initContent();

        $page = (int)Tools::getValue('page', 1);
        $posts_per_page = (int)Configuration::get('STBLOG_POSTS_PER_PAGE', 10);
        $id_lang = $this->context->language->id;
        $id_shop = $this->context->shop->id;

        // Get posts for this category
        $posts = StBlogPost::getPostsByCategory($this->category->id, $id_lang, $page, $posts_per_page, $id_shop);
        $total_posts = StBlogCategory::getPostsCount($this->category->id);

        // Calculate pagination
        $total_pages = ceil($total_posts / $posts_per_page);

        // Get subcategories
        $subcategories = $this->category->getChildren($id_lang, $id_shop);

        // Get all categories for sidebar
        $all_categories = StBlogCategory::getCategories($id_lang, true, $id_shop);

        // Prepare posts data
        foreach ($posts as &$post) {
            $post['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'post',
                ['id_post' => $post['id_stblog_post'], 'post_rewrite' => $post['link_rewrite']]
            );
            
            $post['comments_count'] = StBlogComment::getCommentsCount($post['id_stblog_post']);
            $post['date_formatted'] = Tools::displayDate($post['date_publish'], $id_lang);
        }

        // Prepare subcategories data
        foreach ($subcategories as &$subcategory) {
            $subcategory['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                ['id_category' => $subcategory['id_stblog_category'], 'category_rewrite' => $subcategory['link_rewrite']]
            );
            $subcategory['posts_count'] = StBlogCategory::getPostsCount($subcategory['id_stblog_category']);
        }

        // Prepare all categories data
        foreach ($all_categories as &$cat) {
            $cat['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                ['id_category' => $cat['id_stblog_category'], 'category_rewrite' => $cat['link_rewrite']]
            );
            $cat['posts_count'] = StBlogCategory::getPostsCount($cat['id_stblog_category']);
            $cat['is_current'] = ($cat['id_stblog_category'] == $this->category->id);
        }

        // Pagination URLs
        $pagination = [];
        if ($total_pages > 1) {
            for ($i = 1; $i <= $total_pages; $i++) {
                $pagination[] = [
                    'page' => $i,
                    'url' => $this->context->link->getModuleLink(
                        'stsmartblog',
                        'category',
                        [
                            'id_category' => $this->category->id,
                            'category_rewrite' => $this->category->link_rewrite,
                            'page' => $i
                        ]
                    ),
                    'current' => ($i == $page)
                ];
            }
        }

        // Get breadcrumb
        $breadcrumb = $this->category->getBreadcrumb($id_lang, $id_shop);

        $this->context->smarty->assign([
            'category' => $this->category,
            'posts' => $posts,
            'subcategories' => $subcategories,
            'all_categories' => $all_categories,
            'pagination' => $pagination,
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_posts' => $total_posts,
            'posts_per_page' => $posts_per_page,
            'breadcrumb' => $breadcrumb,
            'module_dir' => $this->module->getPathUri(),
            'blog_url' => $this->context->link->getModuleLink('stsmartblog', 'list'),
            'category_url' => $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                ['id_category' => $this->category->id, 'category_rewrite' => $this->category->link_rewrite]
            )
        ]);

        $this->setTemplate('module:stsmartblog/views/templates/front/category.tpl');
    }

    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();

        // Add blog link
        $breadcrumb['links'][] = [
            'title' => $this->l('Blog'),
            'url' => $this->context->link->getModuleLink('stsmartblog', 'list')
        ];

        // Add category breadcrumb
        $category_breadcrumb = $this->category->getBreadcrumb($this->context->language->id, $this->context->shop->id);
        
        foreach ($category_breadcrumb as $cat) {
            $breadcrumb['links'][] = [
                'title' => $cat['name'],
                'url' => $this->context->link->getModuleLink(
                    'stsmartblog',
                    'category',
                    ['id_category' => $cat['id'], 'category_rewrite' => $cat['link_rewrite']]
                )
            ];
        }

        return $breadcrumb;
    }

    public function getCanonicalURL()
    {
        $page = (int)Tools::getValue('page', 1);
        
        $params = [
            'id_category' => $this->category->id,
            'category_rewrite' => $this->category->link_rewrite
        ];
        
        if ($page > 1) {
            $params['page'] = $page;
        }
        
        return $this->context->link->getModuleLink('stsmartblog', 'category', $params);
    }

    public function getTemplateVarPage()
    {
        $page = parent::getTemplateVarPage();
        
        $page['meta']['title'] = !empty($this->category->meta_title) ? $this->category->meta_title : $this->category->name;
        $page['meta']['description'] = !empty($this->category->meta_description) ? $this->category->meta_description : $this->category->description;
        $page['meta']['keywords'] = $this->category->meta_keywords;
        $page['meta']['robots'] = 'index,follow';
        
        // Add canonical URL
        $page['canonical'] = $this->getCanonicalURL();
        
        // Add pagination meta tags
        $current_page = (int)Tools::getValue('page', 1);
        $total_posts = StBlogCategory::getPostsCount($this->category->id);
        $posts_per_page = (int)Configuration::get('STBLOG_POSTS_PER_PAGE', 10);
        $total_pages = ceil($total_posts / $posts_per_page);
        
        if ($current_page > 1) {
            $page['meta']['prev'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                [
                    'id_category' => $this->category->id,
                    'category_rewrite' => $this->category->link_rewrite,
                    'page' => $current_page - 1
                ]
            );
        }
        
        if ($current_page < $total_pages) {
            $page['meta']['next'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                [
                    'id_category' => $this->category->id,
                    'category_rewrite' => $this->category->link_rewrite,
                    'page' => $current_page + 1
                ]
            );
        }

        return $page;
    }

    public function l($string, $specific = false, $locale = null)
    {
        return Translate::getModuleTranslation('stsmartblog', $string, 'category', $locale);
    }
}

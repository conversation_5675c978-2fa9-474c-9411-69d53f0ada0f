/**
 * ST Smart Blog - Backend CSS
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

/* Admin Panel Styles */
.stblog-admin-wrapper {
    margin: 20px 0;
}

.stblog-config-form .form-group {
    margin-bottom: 20px;
}

.stblog-config-form label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.stblog-config-form .help-block {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
}

/* Blog Post Editor */
.stblog-post-editor {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.stblog-post-meta {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.stblog-post-meta .form-group {
    margin-bottom: 15px;
}

.stblog-post-meta .form-group:last-child {
    margin-bottom: 0;
}

/* Category Tree */
.stblog-category-tree {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
}

.stblog-category-tree ul {
    list-style: none;
    padding-left: 0;
    margin: 0;
}

.stblog-category-tree ul ul {
    padding-left: 20px;
    margin-top: 5px;
}

.stblog-category-tree li {
    margin-bottom: 5px;
    padding: 5px;
    border-radius: 3px;
    transition: background-color 0.3s ease;
}

.stblog-category-tree li:hover {
    background: #f8f9fa;
}

.stblog-category-tree a {
    color: #333;
    text-decoration: none;
}

.stblog-category-tree a:hover {
    color: #007bff;
}

/* Comments Moderation */
.stblog-comment-item {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 15px;
    margin-bottom: 15px;
}

.stblog-comment-header {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid #eee;
}

.stblog-comment-author {
    font-weight: 600;
    color: #333;
}

.stblog-comment-date {
    color: #666;
    font-size: 12px;
    margin-left: 10px;
}

.stblog-comment-content {
    line-height: 1.6;
    color: #555;
    margin-bottom: 10px;
}

.stblog-comment-actions {
    text-align: right;
}

.stblog-comment-actions .btn {
    margin-left: 5px;
}

/* Status Indicators */
.stblog-status-active {
    color: #28a745;
    font-weight: 600;
}

.stblog-status-inactive {
    color: #dc3545;
    font-weight: 600;
}

.stblog-status-pending {
    color: #ffc107;
    font-weight: 600;
}

/* Statistics Cards */
.stblog-stats-card {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
}

.stblog-stats-number {
    font-size: 32px;
    font-weight: 600;
    color: #007bff;
    margin-bottom: 5px;
}

.stblog-stats-label {
    color: #666;
    font-size: 14px;
}

/* Tag Management */
.stblog-tag-cloud {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
}

.stblog-tag-item {
    display: inline-block;
    margin: 0 5px 5px 0;
    padding: 3px 8px;
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.stblog-tag-item:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

.stblog-tag-item.selected {
    background: #007bff;
    color: white;
}

/* Import/Export */
.stblog-import-export {
    background: #fff;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 20px;
}

.stblog-import-progress {
    margin-top: 15px;
}

.stblog-import-log {
    background: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 10px;
    max-height: 200px;
    overflow-y: auto;
    font-family: monospace;
    font-size: 12px;
    margin-top: 15px;
}

/* Responsive Admin */
@media (max-width: 768px) {
    .stblog-post-editor {
        padding: 15px;
    }
    
    .stblog-post-meta {
        padding: 10px;
    }
    
    .stblog-comment-item {
        padding: 10px;
    }
    
    .stblog-stats-card {
        padding: 15px;
    }
    
    .stblog-stats-number {
        font-size: 24px;
    }
}

/* Custom Buttons */
.btn-stblog-primary {
    background-color: #007bff;
    border-color: #007bff;
    color: white;
}

.btn-stblog-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    color: white;
}

.btn-stblog-success {
    background-color: #28a745;
    border-color: #28a745;
    color: white;
}

.btn-stblog-success:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
    color: white;
}

.btn-stblog-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
}

.btn-stblog-warning:hover {
    background-color: #e0a800;
    border-color: #e0a800;
    color: #212529;
}

.btn-stblog-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    color: white;
}

.btn-stblog-danger:hover {
    background-color: #c82333;
    border-color: #c82333;
    color: white;
}

/* Loading Spinner */
.stblog-loading {
    text-align: center;
    padding: 20px;
}

.stblog-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #007bff;
    border-radius: 50%;
    animation: stblog-spin 1s linear infinite;
}

@keyframes stblog-spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alerts */
.stblog-alert {
    padding: 12px 20px;
    margin-bottom: 20px;
    border: 1px solid transparent;
    border-radius: 4px;
}

.stblog-alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.stblog-alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

.stblog-alert-warning {
    color: #856404;
    background-color: #fff3cd;
    border-color: #ffeaa7;
}

.stblog-alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

<?php
/**
 * ST Smart Blog - Blog Tag Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StBlogTag extends ObjectModel
{
    /** @var int */
    public $id_stblog_tag;

    /** @var bool */
    public $active;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    /** @var string */
    public $name;

    /** @var string */
    public $meta_title;

    /** @var string */
    public $meta_description;

    /** @var string */
    public $meta_keywords;

    /** @var string */
    public $link_rewrite;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'stblog_tag',
        'primary' => 'id_stblog_tag',
        'multilang' => true,
        'multilang_shop' => true,
        'fields' => [
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],

            // Lang fields
            'name' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'required' => true, 'size' => 255],
            'meta_title' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 255],
            'meta_description' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 512],
            'meta_keywords' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 255],
            'link_rewrite' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isLinkRewrite', 'required' => true, 'size' => 128],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    public function delete()
    {
        // Remove tag associations with posts
        Db::getInstance()->delete('stblog_post_tag', 'id_stblog_tag = ' . (int)$this->id);

        return parent::delete();
    }

    /**
     * Get all tags
     */
    public static function getTags($id_lang, $active = true, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT t.*, tl.name, tl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_tag` t
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_tag_lang` tl ON (t.id_stblog_tag = tl.id_stblog_tag)
                WHERE tl.id_lang = ' . (int)$id_lang . '
                AND tl.id_shop = ' . (int)$id_shop;

        if ($active) {
            $sql .= ' AND t.active = 1';
        }

        $sql .= ' ORDER BY tl.name ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get tags by post
     */
    public static function getTagsByPost($id_post, $id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT t.*, tl.name, tl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_tag` t
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_tag_lang` tl ON (t.id_stblog_tag = tl.id_stblog_tag)
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_tag` pt ON (t.id_stblog_tag = pt.id_stblog_tag)
                WHERE pt.id_stblog_post = ' . (int)$id_post . '
                AND t.active = 1
                AND tl.id_lang = ' . (int)$id_lang . '
                AND tl.id_shop = ' . (int)$id_shop . '
                ORDER BY tl.name ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get posts by tag
     */
    public static function getPostsByTag($id_tag, $id_lang, $page = 1, $limit = 10, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $start = ($page - 1) * $limit;

        $sql = 'SELECT p.*, pl.title, pl.content_short, pl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_post` p
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_tag` pt ON (p.id_stblog_post = pt.id_stblog_post)
                WHERE pt.id_stblog_tag = ' . (int)$id_tag . '
                AND p.active = 1
                AND p.date_publish <= NOW()
                AND pl.id_lang = ' . (int)$id_lang . '
                AND pl.id_shop = ' . (int)$id_shop . '
                ORDER BY p.date_publish DESC
                LIMIT ' . (int)$start . ', ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get tag by link rewrite
     */
    public static function getTagByRewrite($link_rewrite, $id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT t.id_stblog_tag
                FROM `' . _DB_PREFIX_ . 'stblog_tag` t
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_tag_lang` tl ON (t.id_stblog_tag = tl.id_stblog_tag)
                WHERE t.active = 1 
                AND tl.link_rewrite = "' . pSQL($link_rewrite) . '"
                AND tl.id_lang = ' . (int)$id_lang . '
                AND tl.id_shop = ' . (int)$id_shop;

        $id_tag = Db::getInstance()->getValue($sql);
        
        if ($id_tag) {
            return new StBlogTag($id_tag, $id_lang, $id_shop);
        }
        
        return false;
    }

    /**
     * Get popular tags (by post count)
     */
    public static function getPopularTags($id_lang, $limit = 20, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT t.*, tl.name, tl.link_rewrite, COUNT(pt.id_stblog_post) as posts_count
                FROM `' . _DB_PREFIX_ . 'stblog_tag` t
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_tag_lang` tl ON (t.id_stblog_tag = tl.id_stblog_tag)
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_tag` pt ON (t.id_stblog_tag = pt.id_stblog_tag)
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post` p ON (pt.id_stblog_post = p.id_stblog_post)
                WHERE t.active = 1
                AND p.active = 1
                AND p.date_publish <= NOW()
                AND tl.id_lang = ' . (int)$id_lang . '
                AND tl.id_shop = ' . (int)$id_shop . '
                GROUP BY t.id_stblog_tag
                HAVING posts_count > 0
                ORDER BY posts_count DESC, tl.name ASC
                LIMIT ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get tag cloud data
     */
    public static function getTagCloud($id_lang, $limit = 50, $id_shop = null)
    {
        $tags = self::getPopularTags($id_lang, $limit, $id_shop);
        
        if (empty($tags)) {
            return [];
        }

        // Calculate tag weights for cloud display
        $max_count = max(array_column($tags, 'posts_count'));
        $min_count = min(array_column($tags, 'posts_count'));
        
        foreach ($tags as &$tag) {
            // Calculate weight (1-5 scale)
            if ($max_count == $min_count) {
                $tag['weight'] = 3;
            } else {
                $tag['weight'] = round(1 + (($tag['posts_count'] - $min_count) / ($max_count - $min_count)) * 4);
            }
        }

        return $tags;
    }

    /**
     * Add tag to post
     */
    public static function addTagToPost($id_tag, $id_post)
    {
        $sql = 'INSERT IGNORE INTO `' . _DB_PREFIX_ . 'stblog_post_tag` 
                (id_stblog_tag, id_stblog_post) 
                VALUES (' . (int)$id_tag . ', ' . (int)$id_post . ')';

        return Db::getInstance()->execute($sql);
    }

    /**
     * Remove tag from post
     */
    public static function removeTagFromPost($id_tag, $id_post)
    {
        return Db::getInstance()->delete(
            'stblog_post_tag',
            'id_stblog_tag = ' . (int)$id_tag . ' AND id_stblog_post = ' . (int)$id_post
        );
    }

    /**
     * Set tags for post
     */
    public static function setTagsForPost($id_post, $tags, $id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        // Remove existing tags
        Db::getInstance()->delete('stblog_post_tag', 'id_stblog_post = ' . (int)$id_post);

        if (empty($tags)) {
            return true;
        }

        // Process tags
        foreach ($tags as $tag_name) {
            $tag_name = trim($tag_name);
            if (empty($tag_name)) {
                continue;
            }

            // Check if tag exists
            $link_rewrite = Tools::link_rewrite($tag_name);
            $existing_tag = self::getTagByName($tag_name, $id_lang, $id_shop);

            if ($existing_tag) {
                $id_tag = $existing_tag->id;
            } else {
                // Create new tag
                $tag = new StBlogTag();
                $tag->active = 1;
                $tag->name = [$id_lang => $tag_name];
                $tag->link_rewrite = [$id_lang => $link_rewrite];
                $tag->meta_title = [$id_lang => $tag_name];
                
                if ($tag->add()) {
                    $id_tag = $tag->id;
                } else {
                    continue;
                }
            }

            // Associate tag with post
            self::addTagToPost($id_tag, $id_post);
        }

        return true;
    }

    /**
     * Get tag by name
     */
    public static function getTagByName($name, $id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT t.id_stblog_tag
                FROM `' . _DB_PREFIX_ . 'stblog_tag` t
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_tag_lang` tl ON (t.id_stblog_tag = tl.id_stblog_tag)
                WHERE tl.name = "' . pSQL($name) . '"
                AND tl.id_lang = ' . (int)$id_lang . '
                AND tl.id_shop = ' . (int)$id_shop;

        $id_tag = Db::getInstance()->getValue($sql);
        
        if ($id_tag) {
            return new StBlogTag($id_tag, $id_lang, $id_shop);
        }
        
        return false;
    }

    /**
     * Get posts count for tag
     */
    public function getPostsCount()
    {
        $sql = 'SELECT COUNT(DISTINCT pt.id_stblog_post)
                FROM `' . _DB_PREFIX_ . 'stblog_post_tag` pt
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post` p ON (pt.id_stblog_post = p.id_stblog_post)
                WHERE pt.id_stblog_tag = ' . (int)$this->id . '
                AND p.active = 1
                AND p.date_publish <= NOW()';

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Search tags by name
     */
    public static function searchTags($query, $id_lang, $limit = 10, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT t.*, tl.name, tl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_tag` t
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_tag_lang` tl ON (t.id_stblog_tag = tl.id_stblog_tag)
                WHERE t.active = 1
                AND tl.name LIKE "%' . pSQL($query) . '%"
                AND tl.id_lang = ' . (int)$id_lang . '
                AND tl.id_shop = ' . (int)$id_shop . '
                ORDER BY tl.name ASC
                LIMIT ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }
}

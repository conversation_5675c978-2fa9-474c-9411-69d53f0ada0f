/**
 * ST Smart Blog - Backend JavaScript
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

$(document).ready(function() {
    
    // Auto-generate link rewrite from title
    $('input[name*="title"]').on('keyup', function() {
        var title = $(this).val();
        var langId = $(this).attr('name').match(/\[(\d+)\]/);
        
        if (langId && title) {
            var linkRewrite = generateLinkRewrite(title);
            var linkRewriteInput = $('input[name="link_rewrite[' + langId[1] + ']"]');
            
            if (linkRewriteInput.length && !linkRewriteInput.val()) {
                linkRewriteInput.val(linkRewrite);
            }
        }
    });
    
    // Tag management
    initTagManagement();
    
    // Category tree management
    initCategoryTree();
    
    // Comment moderation
    initCommentModeration();
    
    // Import/Export functionality
    initImportExport();
    
    // Configuration form handling
    initConfigurationForm();
    
    // Rich text editor enhancements
    initRichTextEditor();
    
    // Image upload handling
    initImageUpload();
    
    // Bulk actions
    initBulkActions();
    
    // Auto-save functionality
    initAutoSave();
    
    // Helper functions
    function generateLinkRewrite(title) {
        return title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
    }
    
    function initTagManagement() {
        // Tag input with autocomplete
        $('.tag-input').each(function() {
            var input = $(this);
            var tagContainer = input.siblings('.tag-container');
            
            input.on('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ',') {
                    e.preventDefault();
                    addTag(input.val().trim(), tagContainer, input);
                    input.val('');
                }
            });
            
            // Tag autocomplete
            input.autocomplete({
                source: function(request, response) {
                    $.ajax({
                        url: adminUrl + 'ajax-tab.php',
                        data: {
                            ajax: true,
                            action: 'searchTags',
                            query: request.term,
                            token: token
                        },
                        success: function(data) {
                            response(data);
                        }
                    });
                },
                minLength: 2,
                select: function(event, ui) {
                    addTag(ui.item.value, tagContainer, input);
                    input.val('');
                    return false;
                }
            });
        });
        
        // Remove tag functionality
        $(document).on('click', '.tag-remove', function() {
            $(this).parent().remove();
            updateTagsInput();
        });
        
        function addTag(tagName, container, input) {
            if (!tagName) return;
            
            // Check if tag already exists
            var exists = container.find('.tag-item').filter(function() {
                return $(this).text().trim() === tagName;
            }).length > 0;
            
            if (!exists) {
                var tagElement = $('<span class="tag-item">' + tagName + ' <span class="tag-remove">×</span></span>');
                container.append(tagElement);
                updateTagsInput();
            }
        }
        
        function updateTagsInput() {
            var tags = [];
            $('.tag-container .tag-item').each(function() {
                var tagText = $(this).text().replace('×', '').trim();
                if (tagText) {
                    tags.push(tagText);
                }
            });
            $('input[name="tags"]').val(tags.join(','));
        }
    }
    
    function initCategoryTree() {
        // Sortable category tree
        if ($.fn.sortable) {
            $('.category-tree').sortable({
                handle: '.category-handle',
                placeholder: 'category-placeholder',
                update: function(event, ui) {
                    updateCategoryPositions();
                }
            });
        }
        
        // Category expand/collapse
        $('.category-toggle').on('click', function() {
            var categoryItem = $(this).closest('.category-item');
            var children = categoryItem.find('.category-children').first();
            
            if (children.is(':visible')) {
                children.slideUp();
                $(this).removeClass('expanded');
            } else {
                children.slideDown();
                $(this).addClass('expanded');
            }
        });
        
        function updateCategoryPositions() {
            var positions = {};
            $('.category-item').each(function(index) {
                var categoryId = $(this).data('category-id');
                positions[categoryId] = index + 1;
            });
            
            $.ajax({
                url: adminUrl + 'ajax-tab.php',
                method: 'POST',
                data: {
                    ajax: true,
                    action: 'updateCategoryPositions',
                    positions: positions,
                    token: token
                },
                success: function(response) {
                    if (response.success) {
                        showNotification('Category positions updated', 'success');
                    }
                }
            });
        }
    }
    
    function initCommentModeration() {
        // Approve comment
        $('.approve-comment').on('click', function(e) {
            e.preventDefault();
            moderateComment($(this).data('comment-id'), 'approve', $(this));
        });
        
        // Reject comment
        $('.reject-comment').on('click', function(e) {
            e.preventDefault();
            moderateComment($(this).data('comment-id'), 'reject', $(this));
        });
        
        // Delete comment
        $('.delete-comment').on('click', function(e) {
            e.preventDefault();
            if (confirm('Are you sure you want to delete this comment?')) {
                moderateComment($(this).data('comment-id'), 'delete', $(this));
            }
        });
        
        function moderateComment(commentId, action, button) {
            var originalText = button.text();
            button.prop('disabled', true).text('Processing...');
            
            $.ajax({
                url: adminUrl + 'ajax-tab.php',
                method: 'POST',
                data: {
                    ajax: true,
                    action: 'moderateComment',
                    comment_id: commentId,
                    moderation_action: action,
                    token: token
                },
                success: function(response) {
                    if (response.success) {
                        if (action === 'delete') {
                            button.closest('.comment-item').fadeOut();
                        } else {
                            button.closest('.comment-item').removeClass('pending approved rejected')
                                  .addClass(action === 'approve' ? 'approved' : 'rejected');
                        }
                        showNotification('Comment ' + action + 'd successfully', 'success');
                    } else {
                        showNotification('Error: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showNotification('Error processing request', 'error');
                },
                complete: function() {
                    button.prop('disabled', false).text(originalText);
                }
            });
        }
    }
    
    function initImportExport() {
        // WordPress import
        $('#wordpress-import-form').on('submit', function(e) {
            e.preventDefault();
            
            var formData = new FormData(this);
            var progressBar = $('.import-progress');
            var logContainer = $('.import-log');
            
            progressBar.show();
            logContainer.empty();
            
            $.ajax({
                url: $(this).attr('action'),
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                xhr: function() {
                    var xhr = new window.XMLHttpRequest();
                    xhr.upload.addEventListener('progress', function(evt) {
                        if (evt.lengthComputable) {
                            var percentComplete = evt.loaded / evt.total * 100;
                            progressBar.find('.progress-bar').css('width', percentComplete + '%');
                        }
                    }, false);
                    return xhr;
                },
                success: function(response) {
                    if (response.success) {
                        showNotification('Import completed successfully', 'success');
                        logContainer.html(response.log);
                    } else {
                        showNotification('Import failed: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showNotification('Import failed', 'error');
                }
            });
        });
        
        // Export functionality
        $('.export-btn').on('click', function(e) {
            e.preventDefault();
            
            var exportType = $(this).data('export-type');
            var button = $(this);
            var originalText = button.text();
            
            button.prop('disabled', true).text('Exporting...');
            
            window.location.href = adminUrl + 'ajax-tab.php?ajax=1&action=export&type=' + exportType + '&token=' + token;
            
            setTimeout(function() {
                button.prop('disabled', false).text(originalText);
            }, 3000);
        });
    }
    
    function initConfigurationForm() {
        // Configuration form validation
        $('#stblog-config-form').on('submit', function(e) {
            var form = $(this);
            var isValid = true;
            
            // Validate required fields
            form.find('input[required], select[required], textarea[required]').each(function() {
                if (!$(this).val()) {
                    $(this).addClass('error');
                    isValid = false;
                } else {
                    $(this).removeClass('error');
                }
            });
            
            if (!isValid) {
                e.preventDefault();
                showNotification('Please fill in all required fields', 'error');
            }
        });
        
        // Test API connections
        $('.test-api-btn').on('click', function(e) {
            e.preventDefault();
            
            var apiType = $(this).data('api-type');
            var apiKey = $('input[name="' + apiType + '_api_key"]').val();
            var button = $(this);
            var originalText = button.text();
            
            if (!apiKey) {
                showNotification('Please enter API key first', 'error');
                return;
            }
            
            button.prop('disabled', true).text('Testing...');
            
            $.ajax({
                url: adminUrl + 'ajax-tab.php',
                method: 'POST',
                data: {
                    ajax: true,
                    action: 'testApi',
                    api_type: apiType,
                    api_key: apiKey,
                    token: token
                },
                success: function(response) {
                    if (response.success) {
                        showNotification('API connection successful', 'success');
                    } else {
                        showNotification('API connection failed: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showNotification('API test failed', 'error');
                },
                complete: function() {
                    button.prop('disabled', false).text(originalText);
                }
            });
        });
    }
    
    function initRichTextEditor() {
        // TinyMCE enhancements
        if (typeof tinymce !== 'undefined') {
            tinymce.on('AddEditor', function(e) {
                var editor = e.editor;
                
                // Add custom buttons
                editor.addButton('stblog_shortcode', {
                    text: 'Shortcode',
                    icon: 'code',
                    onclick: function() {
                        editor.windowManager.open({
                            title: 'Insert Shortcode',
                            body: [
                                {type: 'listbox', name: 'shortcode', label: 'Shortcode', values: [
                                    {text: 'Related Posts', value: '[related_posts]'},
                                    {text: 'Recent Posts', value: '[recent_posts limit="5"]'},
                                    {text: 'Category Posts', value: '[category_posts id="1"]'},
                                    {text: 'Tag Cloud', value: '[tag_cloud]'}
                                ]}
                            ],
                            onsubmit: function(e) {
                                editor.insertContent(e.data.shortcode);
                            }
                        });
                    }
                });
            });
        }
    }
    
    function initImageUpload() {
        // Drag and drop image upload
        $('.image-upload-area').on('dragover', function(e) {
            e.preventDefault();
            $(this).addClass('dragover');
        });
        
        $('.image-upload-area').on('dragleave', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
        });
        
        $('.image-upload-area').on('drop', function(e) {
            e.preventDefault();
            $(this).removeClass('dragover');
            
            var files = e.originalEvent.dataTransfer.files;
            uploadImages(files);
        });
        
        function uploadImages(files) {
            for (var i = 0; i < files.length; i++) {
                var file = files[i];
                if (file.type.startsWith('image/')) {
                    uploadSingleImage(file);
                }
            }
        }
        
        function uploadSingleImage(file) {
            var formData = new FormData();
            formData.append('image', file);
            formData.append('token', token);
            
            $.ajax({
                url: adminUrl + 'ajax-tab.php?action=uploadImage',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.success) {
                        insertImageIntoEditor(response.image_url);
                        showNotification('Image uploaded successfully', 'success');
                    } else {
                        showNotification('Image upload failed: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showNotification('Image upload failed', 'error');
                }
            });
        }
        
        function insertImageIntoEditor(imageUrl) {
            if (typeof tinymce !== 'undefined' && tinymce.activeEditor) {
                tinymce.activeEditor.insertContent('<img src="' + imageUrl + '" alt="" />');
            }
        }
    }
    
    function initBulkActions() {
        // Select all checkbox
        $('#select-all').on('change', function() {
            $('.item-checkbox').prop('checked', $(this).is(':checked'));
            updateBulkActionButtons();
        });
        
        // Individual checkboxes
        $('.item-checkbox').on('change', function() {
            updateBulkActionButtons();
        });
        
        // Bulk action buttons
        $('.bulk-action-btn').on('click', function(e) {
            e.preventDefault();
            
            var action = $(this).data('action');
            var selectedItems = $('.item-checkbox:checked').map(function() {
                return $(this).val();
            }).get();
            
            if (selectedItems.length === 0) {
                showNotification('Please select items first', 'error');
                return;
            }
            
            if (confirm('Are you sure you want to ' + action + ' ' + selectedItems.length + ' item(s)?')) {
                performBulkAction(action, selectedItems);
            }
        });
        
        function updateBulkActionButtons() {
            var selectedCount = $('.item-checkbox:checked').length;
            $('.bulk-action-btn').prop('disabled', selectedCount === 0);
            $('.selected-count').text(selectedCount);
        }
        
        function performBulkAction(action, items) {
            $.ajax({
                url: adminUrl + 'ajax-tab.php',
                method: 'POST',
                data: {
                    ajax: true,
                    action: 'bulkAction',
                    bulk_action: action,
                    items: items,
                    token: token
                },
                success: function(response) {
                    if (response.success) {
                        showNotification(response.message, 'success');
                        location.reload();
                    } else {
                        showNotification('Bulk action failed: ' + response.message, 'error');
                    }
                },
                error: function() {
                    showNotification('Bulk action failed', 'error');
                }
            });
        }
    }
    
    function initAutoSave() {
        var autoSaveInterval;
        var hasChanges = false;
        
        // Track form changes
        $('form input, form textarea, form select').on('change keyup', function() {
            hasChanges = true;
            clearInterval(autoSaveInterval);
            autoSaveInterval = setInterval(autoSave, 30000); // Auto-save every 30 seconds
        });
        
        function autoSave() {
            if (!hasChanges) return;
            
            var form = $('form[data-auto-save="true"]');
            if (form.length === 0) return;
            
            var formData = form.serialize();
            formData += '&auto_save=1';
            
            $.ajax({
                url: form.attr('action'),
                method: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        showNotification('Draft saved', 'info', 2000);
                        hasChanges = false;
                    }
                }
            });
        }
    }
    
    // Utility functions
    function showNotification(message, type, duration) {
        type = type || 'info';
        duration = duration || 5000;
        
        var notification = $('<div class="stblog-notification stblog-notification-' + type + '">' + message + '</div>');
        $('body').append(notification);
        
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, duration);
    }
    
    // Initialize tooltips
    if ($.fn.tooltip) {
        $('[data-toggle="tooltip"]').tooltip();
    }
    
    // Initialize popovers
    if ($.fn.popover) {
        $('[data-toggle="popover"]').popover();
    }
});

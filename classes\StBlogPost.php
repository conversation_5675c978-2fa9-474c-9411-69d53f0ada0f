<?php
/**
 * ST Smart Blog - Blog Post Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StBlogPost extends ObjectModel
{
    /** @var int */
    public $id_stblog_post;

    /** @var int */
    public $id_category;

    /** @var int */
    public $id_employee;

    /** @var int */
    public $position;

    /** @var bool */
    public $active;

    /** @var bool */
    public $featured;

    /** @var bool */
    public $allow_comments;

    /** @var int */
    public $views;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    /** @var string */
    public $date_publish;

    /** @var string */
    public $title;

    /** @var string */
    public $content;

    /** @var string */
    public $content_short;

    /** @var string */
    public $meta_title;

    /** @var string */
    public $meta_description;

    /** @var string */
    public $meta_keywords;

    /** @var string */
    public $link_rewrite;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'stblog_post',
        'primary' => 'id_stblog_post',
        'multilang' => true,
        'multilang_shop' => true,
        'fields' => [
            'id_category' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_employee' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'position' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'featured' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'allow_comments' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'views' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_publish' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],

            // Lang fields
            'title' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'required' => true, 'size' => 255],
            'content' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml'],
            'content_short' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml'],
            'meta_title' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 255],
            'meta_description' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 512],
            'meta_keywords' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 255],
            'link_rewrite' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isLinkRewrite', 'required' => true, 'size' => 128],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    public function add($autodate = true, $null_values = false)
    {
        if (!$this->date_publish) {
            $this->date_publish = date('Y-m-d H:i:s');
        }

        if (empty($this->position)) {
            $this->position = $this->getHighestPosition() + 1;
        }

        return parent::add($autodate, $null_values);
    }

    public function update($null_values = false)
    {
        return parent::update($null_values);
    }

    public function delete()
    {
        // Delete associated comments
        $comments = StBlogComment::getCommentsByPost($this->id);
        foreach ($comments as $comment) {
            $comment_obj = new StBlogComment($comment['id_stblog_comment']);
            $comment_obj->delete();
        }

        // Delete associated tags
        Db::getInstance()->delete('stblog_post_tag', 'id_stblog_post = ' . (int)$this->id);

        // Delete associated products
        Db::getInstance()->delete('stblog_post_product', 'id_stblog_post = ' . (int)$this->id);

        return parent::delete();
    }

    /**
     * Get highest position
     */
    public function getHighestPosition()
    {
        $sql = 'SELECT MAX(position) FROM `' . _DB_PREFIX_ . 'stblog_post`';
        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Get latest posts
     */
    public static function getLatestPosts($id_lang, $limit = 10, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT p.*, pl.title, pl.content_short, pl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_post` p
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                WHERE p.active = 1 
                AND p.date_publish <= NOW()
                AND pl.id_lang = ' . (int)$id_lang . '
                AND pl.id_shop = ' . (int)$id_shop . '
                ORDER BY p.date_publish DESC
                LIMIT ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get featured posts
     */
    public static function getFeaturedPosts($id_lang, $limit = 5, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT p.*, pl.title, pl.content_short, pl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_post` p
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                WHERE p.active = 1 
                AND p.featured = 1
                AND p.date_publish <= NOW()
                AND pl.id_lang = ' . (int)$id_lang . '
                AND pl.id_shop = ' . (int)$id_shop . '
                ORDER BY p.date_publish DESC
                LIMIT ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get posts by category
     */
    public static function getPostsByCategory($id_category, $id_lang, $page = 1, $limit = 10, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $start = ($page - 1) * $limit;

        $sql = 'SELECT p.*, pl.title, pl.content_short, pl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_post` p
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                WHERE p.active = 1 
                AND p.id_category = ' . (int)$id_category . '
                AND p.date_publish <= NOW()
                AND pl.id_lang = ' . (int)$id_lang . '
                AND pl.id_shop = ' . (int)$id_shop . '
                ORDER BY p.date_publish DESC
                LIMIT ' . (int)$start . ', ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get related posts by product
     */
    public static function getRelatedPosts($id_product, $id_lang, $limit = 5, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT p.*, pl.title, pl.content_short, pl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_post` p
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_product` pp ON (p.id_stblog_post = pp.id_stblog_post)
                WHERE p.active = 1 
                AND pp.id_product = ' . (int)$id_product . '
                AND p.date_publish <= NOW()
                AND pl.id_lang = ' . (int)$id_lang . '
                AND pl.id_shop = ' . (int)$id_shop . '
                ORDER BY p.date_publish DESC
                LIMIT ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Increment views counter
     */
    public function incrementViews()
    {
        $sql = 'UPDATE `' . _DB_PREFIX_ . 'stblog_post` 
                SET views = views + 1 
                WHERE id_stblog_post = ' . (int)$this->id;
        
        return Db::getInstance()->execute($sql);
    }

    /**
     * Get post by link rewrite
     */
    public static function getPostByRewrite($link_rewrite, $id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT p.id_stblog_post
                FROM `' . _DB_PREFIX_ . 'stblog_post` p
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                WHERE p.active = 1 
                AND pl.link_rewrite = "' . pSQL($link_rewrite) . '"
                AND pl.id_lang = ' . (int)$id_lang . '
                AND pl.id_shop = ' . (int)$id_shop;

        $id_post = Db::getInstance()->getValue($sql);
        
        if ($id_post) {
            return new StBlogPost($id_post, $id_lang, $id_shop);
        }
        
        return false;
    }

    /**
     * Get total posts count
     */
    public static function getTotalPosts($id_category = null, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT COUNT(DISTINCT p.id_stblog_post)
                FROM `' . _DB_PREFIX_ . 'stblog_post` p
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                WHERE p.active = 1 
                AND p.date_publish <= NOW()
                AND pl.id_shop = ' . (int)$id_shop;

        if ($id_category) {
            $sql .= ' AND p.id_category = ' . (int)$id_category;
        }

        return (int)Db::getInstance()->getValue($sql);
    }
}

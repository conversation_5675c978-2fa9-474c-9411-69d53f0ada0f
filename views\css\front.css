/**
 * ST Smart Blog - Frontend CSS
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

/* Blog Wrapper */
.stblog-wrapper {
    margin: 20px 0;
}

/* Blog Post Items */
.stblog-post-item {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.stblog-post-item:last-child {
    border-bottom: none;
}

.post-header {
    margin-bottom: 15px;
}

.post-title {
    margin-bottom: 10px;
    font-size: 24px;
    font-weight: 600;
}

.post-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.post-title a:hover {
    color: #007bff;
}

.post-meta {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

.post-meta span {
    margin-right: 15px;
    display: inline-block;
}

.post-meta i {
    margin-right: 5px;
}

.post-meta a {
    color: #666;
    text-decoration: none;
}

.post-meta a:hover {
    color: #007bff;
}

.post-content {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}

.post-footer {
    margin-top: 15px;
}

/* Sidebar */
.stblog-sidebar {
    padding-left: 20px;
}

.sidebar-widget {
    margin-bottom: 30px;
    background: #f8f9fa;
    padding: 20px;
    border-radius: 5px;
    border: 1px solid #e9ecef;
}

.widget-title {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.widget-content {
    color: #555;
}

/* Category List */
.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-list li {
    margin-bottom: 8px;
    padding: 5px 0;
}

.category-list a {
    color: #333;
    text-decoration: none;
    display: block;
    transition: color 0.3s ease;
}

.category-list a:hover {
    color: #007bff;
}

.post-count {
    color: #666;
    font-size: 12px;
    float: right;
}

/* Featured Posts */
.featured-post-item {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.featured-post-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.featured-post-item h4 {
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 1.4;
}

.featured-post-item a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.featured-post-item a:hover {
    color: #007bff;
}

.featured-post-item .post-date {
    color: #666;
    font-size: 12px;
}

/* Pagination */
.stblog-pagination {
    margin-top: 30px;
    text-align: center;
}

.stblog-pagination .pagination {
    justify-content: center;
}

/* Comments */
.stblog-comments {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #eee;
}

.comments-title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.comment-item {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 5px;
    border-left: 3px solid #007bff;
}

.comment-header {
    margin-bottom: 10px;
    font-size: 14px;
    color: #666;
}

.comment-author {
    font-weight: 600;
    color: #333;
}

.comment-date {
    margin-left: 10px;
}

.comment-content {
    line-height: 1.6;
    color: #555;
}

.comment-reply {
    margin-left: 30px;
    margin-top: 15px;
}

/* Comment Form */
.comment-form {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 5px;
}

.comment-form .form-group {
    margin-bottom: 15px;
}

.comment-form label {
    font-weight: 600;
    margin-bottom: 5px;
    display: block;
}

.comment-form input,
.comment-form textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 3px;
    font-size: 14px;
}

.comment-form textarea {
    min-height: 100px;
    resize: vertical;
}

/* Tags */
.post-tags {
    margin-top: 20px;
}

.tag-item {
    display: inline-block;
    margin: 0 5px 5px 0;
    padding: 3px 8px;
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.tag-item:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

/* Social Share */
.social-share {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.social-share-title {
    margin-bottom: 10px;
    font-weight: 600;
}

.social-share-buttons a {
    display: inline-block;
    margin-right: 10px;
    padding: 8px 12px;
    color: white;
    text-decoration: none;
    border-radius: 3px;
    font-size: 14px;
    transition: opacity 0.3s ease;
}

.social-share-buttons a:hover {
    opacity: 0.8;
    text-decoration: none;
}

.social-facebook { background: #3b5998; }
.social-twitter { background: #1da1f2; }
.social-linkedin { background: #0077b5; }
.social-pinterest { background: #bd081c; }
.social-whatsapp { background: #25d366; }

/* Related Posts */
.related-posts {
    margin-top: 40px;
    padding-top: 30px;
    border-top: 2px solid #eee;
}

.related-posts-title {
    margin-bottom: 20px;
    font-size: 20px;
    font-weight: 600;
}

.related-post-item {
    margin-bottom: 15px;
}

.related-post-item h4 {
    margin-bottom: 5px;
    font-size: 16px;
}

.related-post-item a {
    color: #333;
    text-decoration: none;
}

.related-post-item a:hover {
    color: #007bff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .stblog-sidebar {
        padding-left: 0;
        margin-top: 30px;
    }
    
    .post-meta span {
        display: block;
        margin-bottom: 5px;
        margin-right: 0;
    }
    
    .post-count {
        float: none;
        display: inline;
    }
    
    .comment-reply {
        margin-left: 15px;
    }
    
    .social-share-buttons a {
        margin-bottom: 5px;
    }
}

@media (max-width: 576px) {
    .post-title {
        font-size: 20px;
    }
    
    .sidebar-widget {
        padding: 15px;
    }
    
    .comment-form {
        padding: 15px;
    }
}

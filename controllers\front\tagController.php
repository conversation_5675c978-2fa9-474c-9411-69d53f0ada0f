<?php
/**
 * ST Smart Blog - Tag Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class StSmartBlogTagModuleFrontController extends ModuleFrontController
{
    public $ssl = true;
    public $php_self = 'tag';
    
    protected $tag;
    protected $posts = array();
    protected $nb_posts = 0;
    protected $page = 1;
    protected $nb_pages = 1;
    protected $posts_per_page = 10;
    
    public function init()
    {
        parent::init();
        
        // Get tag ID or name from URL
        $id_tag = (int)Tools::getValue('id_tag');
        $tag_name = Tools::getValue('tag');
        
        if ($id_tag) {
            $this->tag = new StBlogTag($id_tag, $this->context->language->id);
        } elseif ($tag_name) {
            $this->tag = StBlogTag::getByName($tag_name, $this->context->language->id);
        }
        
        if (!Validate::isLoadedObject($this->tag)) {
            Tools::redirect('index.php?controller=404');
        }
        
        // Get pagination settings
        $this->posts_per_page = (int)Configuration::get('STBLOG_POSTS_PER_PAGE', 10);
        $this->page = (int)Tools::getValue('page', 1);
        if ($this->page < 1) {
            $this->page = 1;
        }
        
        // Get posts for this tag
        $this->getPosts();
    }
    
    protected function getPosts()
    {
        $start = ($this->page - 1) * $this->posts_per_page;
        
        // Get total count
        $this->nb_posts = StBlogPost::getPostsByTagCount(
            $this->tag->id,
            $this->context->language->id,
            $this->context->shop->id
        );
        
        // Calculate number of pages
        $this->nb_pages = ceil($this->nb_posts / $this->posts_per_page);
        
        // Get posts
        $this->posts = StBlogPost::getPostsByTag(
            $this->tag->id,
            $this->context->language->id,
            $this->context->shop->id,
            $start,
            $this->posts_per_page
        );
        
        // Process posts
        foreach ($this->posts as &$post) {
            $post['url'] = $this->module->getPostUrl($post['id_stblog_post'], $post['link_rewrite']);
            $post['date_formatted'] = Tools::displayDate($post['date_publish'], null, true);
            $post['category_url'] = $this->module->getCategoryUrl($post['id_stblog_category'], $post['category_link_rewrite']);
            $post['tags'] = StBlogTag::getPostTags($post['id_stblog_post'], $this->context->language->id);
            
            // Process tags URLs
            foreach ($post['tags'] as &$tag) {
                $tag['url'] = $this->module->getTagUrl($tag['id_stblog_tag'], $tag['name']);
            }
            
            $post['comments_count'] = StBlogComment::getCommentsCount($post['id_stblog_post'], true);
        }
    }
    
    public function initContent()
    {
        parent::initContent();
        
        // Set page meta
        $this->context->smarty->assign($this->getTemplateVarPage());
        
        // Get sidebar data
        $sidebar_data = $this->getSidebarData();
        
        // Pagination
        $pagination = $this->getPagination();
        
        $this->context->smarty->assign(array(
            'tag' => array(
                'id_stblog_tag' => $this->tag->id,
                'name' => $this->tag->name,
                'description' => $this->tag->description,
                'url' => $this->module->getTagUrl($this->tag->id, $this->tag->name)
            ),
            'posts' => $this->posts,
            'posts_count' => $this->nb_posts,
            'pagination' => $pagination,
            'blog_url' => $this->module->getBlogUrl(),
            'current_page' => $this->page,
            'total_pages' => $this->nb_pages,
            'posts_per_page' => $this->posts_per_page
        ));
        
        // Merge sidebar data
        $this->context->smarty->assign($sidebar_data);
        
        $this->setTemplate('module:stsmartblog/views/templates/front/tag.tpl');
    }
    
    protected function getSidebarData()
    {
        $data = array();
        
        // Categories
        $data['categories'] = StBlogCategory::getCategories($this->context->language->id, $this->context->shop->id, true);
        foreach ($data['categories'] as &$category) {
            $category['url'] = $this->module->getCategoryUrl($category['id_stblog_category'], $category['link_rewrite']);
            $category['post_count'] = StBlogPost::getPostsByCategoryCount($category['id_stblog_category'], $this->context->language->id, $this->context->shop->id);
        }
        
        // Featured posts
        $data['featured_posts'] = StBlogPost::getFeaturedPosts($this->context->language->id, 5, $this->context->shop->id);
        foreach ($data['featured_posts'] as &$post) {
            $post['url'] = $this->module->getPostUrl($post['id_stblog_post'], $post['link_rewrite']);
            $post['date_formatted'] = Tools::displayDate($post['date_publish'], null, true);
        }
        
        // Recent posts
        $data['recent_posts'] = StBlogPost::getLatestPosts($this->context->language->id, 5, $this->context->shop->id);
        foreach ($data['recent_posts'] as &$post) {
            $post['url'] = $this->module->getPostUrl($post['id_stblog_post'], $post['link_rewrite']);
            $post['date_formatted'] = Tools::displayDate($post['date_publish'], null, true);
        }
        
        // Popular tags
        $data['popular_tags'] = StBlogTag::getPopularTags($this->context->language->id, 20);
        foreach ($data['popular_tags'] as &$tag) {
            $tag['url'] = $this->module->getTagUrl($tag['id_stblog_tag'], $tag['name']);
            $tag['weight'] = min(20, max(12, 12 + ($tag['post_count'] * 2))); // Font size between 12px and 20px
        }
        
        // Archive months
        $data['archive_months'] = StBlogPost::getArchiveMonths($this->context->language->id, $this->context->shop->id);
        foreach ($data['archive_months'] as &$month) {
            $month['url'] = $this->module->getBlogUrl() . '?year=' . $month['year'] . '&month=' . $month['month'];
            $month['label'] = Tools::displayDate($month['year'] . '-' . $month['month'] . '-01', null, false);
        }
        
        return $data;
    }
    
    protected function getPagination()
    {
        if ($this->nb_pages <= 1) {
            return '';
        }
        
        $pagination = new Paginator();
        $pagination->setLimit($this->posts_per_page);
        $pagination->setPage($this->page);
        $pagination->setTotal($this->nb_posts);
        
        $base_url = $this->module->getTagUrl($this->tag->id, $this->tag->name);
        $pagination->setUrl($base_url . (strpos($base_url, '?') !== false ? '&' : '?') . 'page=__PAGE__');
        
        return $pagination->render();
    }
    
    public function getTemplateVarPage()
    {
        $page = parent::getTemplateVarPage();
        
        $page['meta']['title'] = sprintf(
            '%s - %s',
            $this->tag->name,
            Configuration::get('STBLOG_META_TITLE', 'Blog')
        );
        
        $page['meta']['description'] = $this->tag->description ? 
            Tools::substr(strip_tags($this->tag->description), 0, 160) :
            sprintf('Posts tagged with %s', $this->tag->name);
            
        $page['meta']['keywords'] = $this->tag->name;
        
        $page['body_classes']['page-stblog-tag'] = true;
        $page['body_classes']['stblog-tag-' . $this->tag->id] = true;
        
        return $page;
    }
    
    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();
        
        $breadcrumb['links'][] = array(
            'title' => Configuration::get('STBLOG_META_TITLE', 'Blog'),
            'url' => $this->module->getBlogUrl()
        );
        
        $breadcrumb['links'][] = array(
            'title' => sprintf('Tag: %s', $this->tag->name),
            'url' => $this->module->getTagUrl($this->tag->id, $this->tag->name)
        );
        
        return $breadcrumb;
    }
}

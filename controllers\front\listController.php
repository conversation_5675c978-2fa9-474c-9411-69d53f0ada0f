<?php
/**
 * ST Smart Blog - List Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/../../classes/StBlogPost.php';
require_once dirname(__FILE__) . '/../../classes/StBlogCategory.php';

class StSmartBlogListModuleFrontController extends ModuleFrontController
{
    public $ssl = true;

    public function __construct()
    {
        parent::__construct();
        $this->context = Context::getContext();
    }

    public function init()
    {
        parent::init();
    }

    public function initContent()
    {
        parent::initContent();

        $page = (int)Tools::getValue('page', 1);
        $posts_per_page = (int)Configuration::get('STBLOG_POSTS_PER_PAGE', 10);
        $id_lang = $this->context->language->id;
        $id_shop = $this->context->shop->id;

        // Get posts
        $posts = StBlogPost::getLatestPosts($id_lang, $posts_per_page, $id_shop);
        $total_posts = StBlogPost::getTotalPosts(null, $id_shop);

        // Calculate pagination
        $total_pages = ceil($total_posts / $posts_per_page);

        // Get categories for sidebar
        $categories = StBlogCategory::getCategories($id_lang, true, $id_shop);

        // Get featured posts
        $featured_posts = StBlogPost::getFeaturedPosts($id_lang, 3, $id_shop);

        // Prepare posts data
        foreach ($posts as &$post) {
            $post['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'post',
                ['id_post' => $post['id_stblog_post'], 'post_rewrite' => $post['link_rewrite']]
            );
            
            $post['category'] = new StBlogCategory($post['id_category'], $id_lang, $id_shop);
            $post['category_url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                ['id_category' => $post['id_category'], 'category_rewrite' => $post['category']->link_rewrite]
            );

            $post['comments_count'] = StBlogComment::getCommentsCount($post['id_stblog_post']);
            $post['date_formatted'] = Tools::displayDate($post['date_publish'], $id_lang);
        }

        // Prepare categories data
        foreach ($categories as &$category) {
            $category['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                ['id_category' => $category['id_stblog_category'], 'category_rewrite' => $category['link_rewrite']]
            );
            $category['posts_count'] = StBlogCategory::getPostsCount($category['id_stblog_category']);
        }

        // Prepare featured posts data
        foreach ($featured_posts as &$featured_post) {
            $featured_post['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'post',
                ['id_post' => $featured_post['id_stblog_post'], 'post_rewrite' => $featured_post['link_rewrite']]
            );
        }

        // Pagination URLs
        $pagination = [];
        if ($total_pages > 1) {
            for ($i = 1; $i <= $total_pages; $i++) {
                $pagination[] = [
                    'page' => $i,
                    'url' => $this->context->link->getModuleLink('stsmartblog', 'list', ['page' => $i]),
                    'current' => ($i == $page)
                ];
            }
        }

        // SEO
        $this->context->smarty->assign([
            'meta_title' => Configuration::get('STBLOG_META_TITLE', 'Blog'),
            'meta_description' => Configuration::get('STBLOG_META_DESCRIPTION', 'Our latest blog posts'),
            'meta_keywords' => Configuration::get('STBLOG_META_KEYWORDS', 'blog, news, articles')
        ]);

        $this->context->smarty->assign([
            'posts' => $posts,
            'categories' => $categories,
            'featured_posts' => $featured_posts,
            'pagination' => $pagination,
            'current_page' => $page,
            'total_pages' => $total_pages,
            'total_posts' => $total_posts,
            'posts_per_page' => $posts_per_page,
            'blog_title' => $this->l('Blog'),
            'module_dir' => $this->module->getPathUri(),
            'blog_url' => $this->context->link->getModuleLink('stsmartblog', 'list')
        ]);

        $this->setTemplate('module:stsmartblog/views/templates/front/list.tpl');
    }

    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();

        $breadcrumb['links'][] = [
            'title' => $this->l('Blog'),
            'url' => $this->context->link->getModuleLink('stsmartblog', 'list')
        ];

        return $breadcrumb;
    }

    public function getCanonicalURL()
    {
        $page = (int)Tools::getValue('page', 1);
        
        if ($page > 1) {
            return $this->context->link->getModuleLink('stsmartblog', 'list', ['page' => $page]);
        }
        
        return $this->context->link->getModuleLink('stsmartblog', 'list');
    }

    public function getTemplateVarPage()
    {
        $page = parent::getTemplateVarPage();
        
        $page['meta']['title'] = Configuration::get('STBLOG_META_TITLE', 'Blog');
        $page['meta']['description'] = Configuration::get('STBLOG_META_DESCRIPTION', 'Our latest blog posts');
        $page['meta']['keywords'] = Configuration::get('STBLOG_META_KEYWORDS', 'blog, news, articles');
        $page['meta']['robots'] = 'index,follow';
        
        // Add canonical URL
        $page['canonical'] = $this->getCanonicalURL();
        
        // Add pagination meta tags
        $current_page = (int)Tools::getValue('page', 1);
        $total_posts = StBlogPost::getTotalPosts(null, $this->context->shop->id);
        $posts_per_page = (int)Configuration::get('STBLOG_POSTS_PER_PAGE', 10);
        $total_pages = ceil($total_posts / $posts_per_page);
        
        if ($current_page > 1) {
            $page['meta']['prev'] = $this->context->link->getModuleLink('stsmartblog', 'list', ['page' => $current_page - 1]);
        }
        
        if ($current_page < $total_pages) {
            $page['meta']['next'] = $this->context->link->getModuleLink('stsmartblog', 'list', ['page' => $current_page + 1]);
        }

        return $page;
    }

    public function l($string, $specific = false, $locale = null)
    {
        return Translate::getModuleTranslation('stsmartblog', $string, 'list', $locale);
    }
}

/**
 * ST Smart Blog - Frontend JavaScript
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

$(document).ready(function() {
    
    // Comment form handling
    $('#stblog-comment-form').on('submit', function(e) {
        var form = $(this);
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        // Basic validation
        var authorName = form.find('input[name="author_name"]').val().trim();
        var authorEmail = form.find('input[name="author_email"]').val().trim();
        var content = form.find('textarea[name="content"]').val().trim();
        
        if (!authorName) {
            alert('Please enter your name');
            form.find('input[name="author_name"]').focus();
            return false;
        }
        
        if (!authorEmail || !isValidEmail(authorEmail)) {
            alert('Please enter a valid email address');
            form.find('input[name="author_email"]').focus();
            return false;
        }
        
        if (!content) {
            alert('Please enter your comment');
            form.find('textarea[name="content"]').focus();
            return false;
        }
        
        // Show loading state
        submitBtn.prop('disabled', true).text('Posting...');
        
        // Allow form submission to continue
        setTimeout(function() {
            submitBtn.prop('disabled', false).text(originalText);
        }, 3000);
    });
    
    // Reply to comment functionality
    $('.comment-reply-btn').on('click', function(e) {
        e.preventDefault();
        
        var commentId = $(this).data('comment-id');
        var replyForm = $('#stblog-comment-form');
        var parentInput = replyForm.find('input[name="id_parent"]');
        
        // Set parent comment ID
        parentInput.val(commentId);
        
        // Move form after the comment
        var commentItem = $(this).closest('.comment-item');
        replyForm.insertAfter(commentItem);
        
        // Focus on content textarea
        replyForm.find('textarea[name="content"]').focus();
        
        // Update form title
        replyForm.find('.form-title').text('Reply to comment');
        
        // Add cancel button if not exists
        if (!replyForm.find('.cancel-reply-btn').length) {
            var cancelBtn = $('<button type="button" class="btn btn-secondary cancel-reply-btn" style="margin-left: 10px;">Cancel</button>');
            replyForm.find('.form-actions').append(cancelBtn);
        }
    });
    
    // Cancel reply functionality
    $(document).on('click', '.cancel-reply-btn', function() {
        var replyForm = $('#stblog-comment-form');
        var parentInput = replyForm.find('input[name="id_parent"]');
        
        // Reset parent comment ID
        parentInput.val('0');
        
        // Move form back to original position
        replyForm.appendTo('.comment-form-container');
        
        // Reset form title
        replyForm.find('.form-title').text('Leave a comment');
        
        // Remove cancel button
        $(this).remove();
    });
    
    // Social share functionality
    $('.social-share-btn').on('click', function(e) {
        e.preventDefault();
        
        var url = $(this).attr('href');
        var width = 600;
        var height = 400;
        var left = (screen.width / 2) - (width / 2);
        var top = (screen.height / 2) - (height / 2);
        
        window.open(url, 'share', 'width=' + width + ',height=' + height + ',left=' + left + ',top=' + top + ',scrollbars=yes,resizable=yes');
    });
    
    // Search functionality
    $('#stblog-search-form').on('submit', function(e) {
        var searchInput = $(this).find('input[name="search"]');
        var searchTerm = searchInput.val().trim();
        
        if (!searchTerm) {
            e.preventDefault();
            alert('Please enter a search term');
            searchInput.focus();
        }
    });
    
    // Tag cloud interaction
    $('.tag-item').on('click', function(e) {
        // Add loading effect
        $(this).addClass('loading');
    });
    
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        var imageObserver = new IntersectionObserver(function(entries, observer) {
            entries.forEach(function(entry) {
                if (entry.isIntersecting) {
                    var img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(function(img) {
            imageObserver.observe(img);
        });
    }
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(e) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: target.offset().top - 100
            }, 500);
        }
    });
    
    // Newsletter subscription (if enabled)
    $('#stblog-newsletter-form').on('submit', function(e) {
        e.preventDefault();
        
        var form = $(this);
        var email = form.find('input[name="email"]').val().trim();
        var submitBtn = form.find('button[type="submit"]');
        var originalText = submitBtn.text();
        
        if (!email || !isValidEmail(email)) {
            alert('Please enter a valid email address');
            return;
        }
        
        submitBtn.prop('disabled', true).text('Subscribing...');
        
        // AJAX call to subscribe
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                if (response.success) {
                    alert('Thank you for subscribing!');
                    form[0].reset();
                } else {
                    alert(response.message || 'Subscription failed. Please try again.');
                }
            },
            error: function() {
                alert('Subscription failed. Please try again.');
            },
            complete: function() {
                submitBtn.prop('disabled', false).text(originalText);
            }
        });
    });
    
    // Reading progress indicator
    if ($('.post-content').length) {
        var progressBar = $('<div class="reading-progress"><div class="progress-bar"></div></div>');
        $('body').prepend(progressBar);
        
        $(window).on('scroll', function() {
            var scrollTop = $(window).scrollTop();
            var docHeight = $(document).height() - $(window).height();
            var progress = (scrollTop / docHeight) * 100;
            
            $('.progress-bar').css('width', progress + '%');
        });
    }
    
    // Back to top button
    var backToTop = $('<button class="back-to-top" title="Back to top"><i class="fa fa-arrow-up"></i></button>');
    $('body').append(backToTop);
    
    $(window).on('scroll', function() {
        if ($(window).scrollTop() > 300) {
            backToTop.addClass('visible');
        } else {
            backToTop.removeClass('visible');
        }
    });
    
    backToTop.on('click', function() {
        $('html, body').animate({scrollTop: 0}, 500);
    });
    
    // Helper functions
    function isValidEmail(email) {
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // Initialize tooltips if Bootstrap is available
    if (typeof $().tooltip === 'function') {
        $('[data-toggle="tooltip"]').tooltip();
    }
    
    // Initialize popovers if Bootstrap is available
    if (typeof $().popover === 'function') {
        $('[data-toggle="popover"]').popover();
    }
    
    // Print functionality
    $('.print-post-btn').on('click', function(e) {
        e.preventDefault();
        window.print();
    });
    
    // Font size adjustment
    $('.font-size-btn').on('click', function(e) {
        e.preventDefault();
        
        var action = $(this).data('action');
        var content = $('.post-content');
        var currentSize = parseInt(content.css('font-size'));
        
        if (action === 'increase' && currentSize < 20) {
            content.css('font-size', (currentSize + 1) + 'px');
        } else if (action === 'decrease' && currentSize > 12) {
            content.css('font-size', (currentSize - 1) + 'px');
        } else if (action === 'reset') {
            content.css('font-size', '');
        }
        
        // Save preference in localStorage
        localStorage.setItem('stblog_font_size', content.css('font-size'));
    });
    
    // Load saved font size preference
    var savedFontSize = localStorage.getItem('stblog_font_size');
    if (savedFontSize) {
        $('.post-content').css('font-size', savedFontSize);
    }
    
    // Copy link functionality
    $('.copy-link-btn').on('click', function(e) {
        e.preventDefault();
        
        var url = window.location.href;
        
        if (navigator.clipboard) {
            navigator.clipboard.writeText(url).then(function() {
                showNotification('Link copied to clipboard!');
            });
        } else {
            // Fallback for older browsers
            var textArea = document.createElement('textarea');
            textArea.value = url;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification('Link copied to clipboard!');
        }
    });
    
    // Show notification function
    function showNotification(message) {
        var notification = $('<div class="stblog-notification">' + message + '</div>');
        $('body').append(notification);
        
        setTimeout(function() {
            notification.addClass('show');
        }, 100);
        
        setTimeout(function() {
            notification.removeClass('show');
            setTimeout(function() {
                notification.remove();
            }, 300);
        }, 3000);
    }
});

// Add CSS for additional features
$(document).ready(function() {
    var additionalCSS = `
        <style>
        .reading-progress {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 3px;
            background: rgba(0,0,0,0.1);
            z-index: 9999;
        }
        
        .progress-bar {
            height: 100%;
            background: #007bff;
            width: 0%;
            transition: width 0.1s ease;
        }
        
        .back-to-top {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
            z-index: 1000;
        }
        
        .back-to-top.visible {
            opacity: 1;
            visibility: visible;
        }
        
        .back-to-top:hover {
            background: #0056b3;
            transform: translateY(-2px);
        }
        
        .stblog-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 4px;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            z-index: 10000;
        }
        
        .stblog-notification.show {
            opacity: 1;
            transform: translateX(0);
        }
        
        .tag-item.loading {
            opacity: 0.6;
            pointer-events: none;
        }
        
        img.lazy {
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        img.lazy.loaded {
            opacity: 1;
        }
        </style>
    `;
    
    $('head').append(additionalCSS);
});

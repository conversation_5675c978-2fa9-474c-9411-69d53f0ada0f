{*
* ST Smart Blog - Left Column Hook Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Blog Categories' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        {if $categories && count($categories) > 0}
            <ul class="category-list">
                {foreach from=$categories item=category}
                    <li>
                        <a href="{$category.url}" title="{$category.title|escape:'html':'UTF-8'}">
                            {$category.title}
                            <span class="post-count">({$category.post_count})</span>
                        </a>
                    </li>
                {/foreach}
            </ul>
        {else}
            <p>{l s='No categories available' mod='stsmartblog'}</p>
        {/if}
    </div>
</div>

{if $featured_posts && count($featured_posts) > 0}
<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Featured Posts' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        {foreach from=$featured_posts item=post}
            <div class="featured-post-item">
                <h4>
                    <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                        {$post.title|truncate:50:'...'}
                    </a>
                </h4>
                <div class="post-date">
                    <i class="fa fa-calendar"></i>
                    {$post.date_formatted}
                </div>
            </div>
        {/foreach}
    </div>
</div>
{/if}

{if $popular_tags && count($popular_tags) > 0}
<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Popular Tags' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <div class="tag-cloud">
            {foreach from=$popular_tags item=tag}
                <a href="{$tag.url}" class="tag-item" style="font-size: {$tag.weight}px;" title="{$tag.name|escape:'html':'UTF-8'}">
                    {$tag.name}
                </a>
            {/foreach}
        </div>
    </div>
</div>
{/if}

<style>
.stblog-sidebar-widget {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 30px;
}

.stblog-sidebar-widget .widget-title {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.stblog-sidebar-widget .widget-content {
    color: #555;
}

.category-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.category-list li {
    margin-bottom: 8px;
    padding: 5px 0;
}

.category-list a {
    color: #333;
    text-decoration: none;
    display: block;
    transition: color 0.3s ease;
}

.category-list a:hover {
    color: #007bff;
}

.post-count {
    color: #666;
    font-size: 12px;
    float: right;
}

.featured-post-item {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.featured-post-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.featured-post-item h4 {
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 1.4;
}

.featured-post-item a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.featured-post-item a:hover {
    color: #007bff;
}

.featured-post-item .post-date {
    color: #666;
    font-size: 12px;
}

.featured-post-item .post-date i {
    margin-right: 5px;
}

.tag-cloud {
    line-height: 1.8;
}

.tag-cloud .tag-item {
    display: inline-block;
    margin: 0 5px 5px 0;
    padding: 3px 8px;
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.tag-cloud .tag-item:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

@media (max-width: 768px) {
    .stblog-sidebar-widget {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .post-count {
        float: none;
        display: inline;
    }
}
</style>

{*
* ST Smart Blog - Category Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{extends file='page.tpl'}

{block name='page_title'}
    {$category.title} - {l s='Blog Category' mod='stsmartblog'}
{/block}

{block name='page_content'}
<div class="stblog-wrapper">
    <div class="row">
        <div class="col-lg-8 col-md-12">
            <div class="category-header">
                <h1 class="category-title">{$category.title}</h1>
                {if $category.description}
                    <div class="category-description">
                        {$category.description nofilter}
                    </div>
                {/if}
                
                <div class="category-meta">
                    <span class="post-count">
                        <i class="fa fa-file-text"></i>
                        {$posts_count} {if $posts_count == 1}{l s='post' mod='stsmartblog'}{else}{l s='posts' mod='stsmartblog'}{/if}
                    </span>
                </div>
            </div>
            
            {if $subcategories && count($subcategories) > 0}
            <div class="subcategories-section">
                <h3>{l s='Subcategories' mod='stsmartblog'}</h3>
                <div class="row">
                    {foreach from=$subcategories item=subcategory}
                        <div class="col-md-6 col-lg-4">
                            <div class="subcategory-item">
                                <h4>
                                    <a href="{$subcategory.url}" title="{$subcategory.title|escape:'html':'UTF-8'}">
                                        <i class="fa fa-folder"></i>
                                        {$subcategory.title}
                                    </a>
                                </h4>
                                {if $subcategory.description}
                                    <p>{$subcategory.description|strip_tags|truncate:80:'...'}</p>
                                {/if}
                                <div class="subcategory-meta">
                                    <span class="post-count">
                                        {$subcategory.post_count} {if $subcategory.post_count == 1}{l s='post' mod='stsmartblog'}{else}{l s='posts' mod='stsmartblog'}{/if}
                                    </span>
                                </div>
                            </div>
                        </div>
                    {/foreach}
                </div>
            </div>
            {/if}
            
            {if $posts && count($posts) > 0}
                <div class="posts-section">
                    <h3>{l s='Posts in this category' mod='stsmartblog'}</h3>
                    
                    {foreach from=$posts item=post}
                        <article class="stblog-post-item">
                            <div class="post-header">
                                <h2 class="post-title">
                                    <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                                        {$post.title}
                                    </a>
                                </h2>
                                
                                <div class="post-meta">
                                    <span class="post-date">
                                        <i class="fa fa-calendar"></i>
                                        {$post.date_formatted}
                                    </span>
                                    <span class="post-author">
                                        <i class="fa fa-user"></i>
                                        {l s='By' mod='stsmartblog'} {$post.author_name|default:'Admin'}
                                    </span>
                                    <span class="post-views">
                                        <i class="fa fa-eye"></i>
                                        {$post.views} {l s='views' mod='stsmartblog'}
                                    </span>
                                    {if $post.comments_count > 0}
                                    <span class="post-comments">
                                        <i class="fa fa-comments"></i>
                                        <a href="{$post.url}#comments">
                                            {$post.comments_count} {if $post.comments_count == 1}{l s='comment' mod='stsmartblog'}{else}{l s='comments' mod='stsmartblog'}{/if}
                                        </a>
                                    </span>
                                    {/if}
                                </div>
                            </div>
                            
                            <div class="post-content">
                                {if $post.content_short}
                                    {$post.content_short|strip_tags|truncate:300:'...'}
                                {else}
                                    {$post.content|strip_tags|truncate:300:'...'}
                                {/if}
                            </div>
                            
                            <div class="post-footer">
                                <a href="{$post.url}" class="btn btn-primary btn-sm">
                                    {l s='Read More' mod='stsmartblog'}
                                </a>
                                
                                {if $post.tags && count($post.tags) > 0}
                                    <div class="post-tags">
                                        {foreach from=$post.tags item=tag name=tags}
                                            <a href="{$tag.url}" class="tag-item" title="{$tag.name|escape:'html':'UTF-8'}">
                                                {$tag.name}
                                            </a>
                                            {if !$smarty.foreach.tags.last}, {/if}
                                        {/foreach}
                                    </div>
                                {/if}
                            </div>
                        </article>
                    {/foreach}
                    
                    {if $pagination}
                        <div class="stblog-pagination">
                            {$pagination nofilter}
                        </div>
                    {/if}
                </div>
            {else}
                <div class="no-posts-message">
                    <div class="text-center">
                        <i class="fa fa-file-text-o fa-3x text-muted mb-3"></i>
                        <h4>{l s='No posts found' mod='stsmartblog'}</h4>
                        <p class="text-muted">
                            {l s='There are currently no posts in this category.' mod='stsmartblog'}
                        </p>
                        <a href="{$blog_url}" class="btn btn-primary">
                            {l s='Browse All Posts' mod='stsmartblog'}
                        </a>
                    </div>
                </div>
            {/if}
        </div>
        
        <div class="col-lg-4 col-md-12">
            <aside class="stblog-sidebar">
                {include file='module:stsmartblog/views/templates/front/sidebar.tpl'}
            </aside>
        </div>
    </div>
</div>

<style>
.category-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.category-title {
    margin-bottom: 15px;
    font-size: 32px;
    font-weight: 600;
    color: #333;
}

.category-description {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
    font-size: 16px;
}

.category-meta {
    color: #666;
    font-size: 14px;
}

.category-meta i {
    margin-right: 5px;
}

.subcategories-section {
    margin-bottom: 40px;
    padding-bottom: 30px;
    border-bottom: 1px solid #eee;
}

.subcategories-section h3 {
    margin-bottom: 20px;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.subcategory-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.subcategory-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.subcategory-item h4 {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.subcategory-item h4 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.subcategory-item h4 a:hover {
    color: #007bff;
}

.subcategory-item h4 i {
    margin-right: 8px;
    color: #007bff;
}

.subcategory-item p {
    margin-bottom: 10px;
    color: #555;
    line-height: 1.5;
}

.subcategory-meta {
    color: #666;
    font-size: 13px;
}

.posts-section h3 {
    margin-bottom: 25px;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.no-posts-message {
    padding: 60px 20px;
    text-align: center;
}

.no-posts-message .fa {
    color: #dee2e6;
}

.no-posts-message h4 {
    margin-bottom: 15px;
    color: #6c757d;
}

.no-posts-message p {
    margin-bottom: 25px;
}

@media (max-width: 768px) {
    .category-title {
        font-size: 28px;
    }
    
    .category-description {
        font-size: 15px;
    }
    
    .subcategories-section h3,
    .posts-section h3 {
        font-size: 20px;
    }
    
    .subcategory-item {
        padding: 15px;
    }
    
    .subcategory-item h4 {
        font-size: 16px;
    }
    
    .no-posts-message {
        padding: 40px 15px;
    }
}

@media (max-width: 576px) {
    .category-title {
        font-size: 24px;
    }
    
    .subcategory-item h4 {
        font-size: 15px;
    }
}
</style>
{/block}

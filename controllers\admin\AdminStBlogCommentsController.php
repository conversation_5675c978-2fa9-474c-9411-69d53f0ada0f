<?php
/**
 * ST Smart Blog - Admin Comments Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class AdminStBlogCommentsController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'stblog_comment';
        $this->className = 'StBlogComment';
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->addRowAction('approve');
        $this->addRowAction('reject');
        
        $this->bulk_actions = array(
            'approve' => array(
                'text' => $this->l('Approve selected'),
                'icon' => 'icon-check'
            ),
            'reject' => array(
                'text' => $this->l('Reject selected'),
                'icon' => 'icon-remove'
            ),
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?'),
                'icon' => 'icon-trash'
            )
        );

        $this->bootstrap = true;
        $this->context = Context::getContext();

        parent::__construct();

        $this->meta_title = $this->l('Blog Comments');

        $this->fields_list = array(
            'id_stblog_comment' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'author_name' => array(
                'title' => $this->l('Author'),
                'width' => 120,
                'filter_key' => 'a!author_name'
            ),
            'author_email' => array(
                'title' => $this->l('Email'),
                'width' => 150,
                'filter_key' => 'a!author_email'
            ),
            'post_title' => array(
                'title' => $this->l('Post'),
                'width' => 200,
                'orderby' => false,
                'search' => false
            ),
            'content' => array(
                'title' => $this->l('Comment'),
                'width' => 300,
                'maxlength' => 100,
                'orderby' => false,
                'search' => false,
                'callback' => 'getContentClean'
            ),
            'approved' => array(
                'title' => $this->l('Status'),
                'align' => 'center',
                'type' => 'bool',
                'class' => 'fixed-width-sm',
                'callback' => 'getApprovalStatus'
            ),
            'date_add' => array(
                'title' => $this->l('Date'),
                'width' => 120,
                'type' => 'datetime',
                'align' => 'right'
            )
        );

        $this->_select = 'pl.title as post_title';
        $this->_join = 'LEFT JOIN `'._DB_PREFIX_.'stblog_post_lang` pl ON (pl.id_stblog_post = a.id_stblog_post AND pl.id_lang = '.(int)$this->context->language->id.')';
        $this->_orderBy = 'date_add';
        $this->_orderWay = 'DESC';
    }

    public function getContentClean($content)
    {
        return Tools::truncateString(strip_tags($content), 100);
    }

    public function getApprovalStatus($approved, $row)
    {
        if ($approved == 1) {
            return '<span class="label label-success">'.$this->l('Approved').'</span>';
        } elseif ($approved == 0) {
            return '<span class="label label-warning">'.$this->l('Pending').'</span>';
        } else {
            return '<span class="label label-danger">'.$this->l('Rejected').'</span>';
        }
    }

    public function renderForm()
    {
        $this->fields_form = array(
            'legend' => array(
                'title' => $this->l('Comment'),
                'icon' => 'icon-comments'
            ),
            'input' => array(
                array(
                    'type' => 'text',
                    'label' => $this->l('Author Name'),
                    'name' => 'author_name',
                    'required' => true,
                    'col' => 6
                ),
                array(
                    'type' => 'text',
                    'label' => $this->l('Author Email'),
                    'name' => 'author_email',
                    'required' => true,
                    'col' => 6
                ),
                array(
                    'type' => 'text',
                    'label' => $this->l('Author Website'),
                    'name' => 'author_website',
                    'col' => 6
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Comment'),
                    'name' => 'content',
                    'required' => true,
                    'rows' => 8,
                    'col' => 6
                ),
                array(
                    'type' => 'select',
                    'label' => $this->l('Status'),
                    'name' => 'approved',
                    'options' => array(
                        'query' => array(
                            array('id' => '0', 'name' => $this->l('Pending')),
                            array('id' => '1', 'name' => $this->l('Approved')),
                            array('id' => '-1', 'name' => $this->l('Rejected'))
                        ),
                        'id' => 'id',
                        'name' => 'name'
                    ),
                    'col' => 3
                )
            ),
            'submit' => array(
                'title' => $this->l('Save')
            )
        );

        if (!($comment = $this->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    public function displayApproveLink($token, $id)
    {
        $this->context->smarty->assign(array(
            'href' => self::$currentIndex.
                '&'.$this->identifier.'='.$id.
                '&approve'.$this->table.
                '&token='.($token != null ? $token : $this->token),
            'action' => $this->l('Approve'),
            'icon' => 'icon-check'
        ));

        return $this->context->smarty->fetch('helpers/list/list_action_approve.tpl');
    }

    public function displayRejectLink($token, $id)
    {
        $this->context->smarty->assign(array(
            'href' => self::$currentIndex.
                '&'.$this->identifier.'='.$id.
                '&reject'.$this->table.
                '&token='.($token != null ? $token : $this->token),
            'action' => $this->l('Reject'),
            'icon' => 'icon-remove'
        ));

        return $this->context->smarty->fetch('helpers/list/list_action_reject.tpl');
    }

    public function processApprove()
    {
        if (Validate::isLoadedObject($object = $this->loadObject())) {
            $object->approved = 1;
            if ($object->update()) {
                $this->confirmations[] = $this->l('Comment approved successfully.');
            } else {
                $this->errors[] = Tools::displayError('An error occurred while approving the comment.');
            }
        } else {
            $this->errors[] = Tools::displayError('An error occurred while loading the object.');
        }
    }

    public function processReject()
    {
        if (Validate::isLoadedObject($object = $this->loadObject())) {
            $object->approved = -1;
            if ($object->update()) {
                $this->confirmations[] = $this->l('Comment rejected successfully.');
            } else {
                $this->errors[] = Tools::displayError('An error occurred while rejecting the comment.');
            }
        } else {
            $this->errors[] = Tools::displayError('An error occurred while loading the object.');
        }
    }

    public function processBulkApprove()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $object = new $this->className();
            
            if (isset($object->approved)) {
                foreach ($this->boxes as $id) {
                    $to_approve = new $this->className((int)$id);
                    $to_approve->approved = 1;
                    $to_approve->update();
                }
                $this->confirmations[] = $this->l('The selection has been successfully approved.');
            } else {
                $this->errors[] = Tools::displayError('You cannot approve this selection.');
            }
        } else {
            $this->errors[] = Tools::displayError('You must select at least one element to approve.');
        }
    }

    public function processBulkReject()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $object = new $this->className();
            
            if (isset($object->approved)) {
                foreach ($this->boxes as $id) {
                    $to_reject = new $this->className((int)$id);
                    $to_reject->approved = -1;
                    $to_reject->update();
                }
                $this->confirmations[] = $this->l('The selection has been successfully rejected.');
            } else {
                $this->errors[] = Tools::displayError('You cannot reject this selection.');
            }
        } else {
            $this->errors[] = Tools::displayError('You must select at least one element to reject.');
        }
    }

    public function initToolbar()
    {
        parent::initToolbar();
        
        // Remove the "Add new" button since comments are created from frontend
        unset($this->toolbar_btn['new']);
    }
}

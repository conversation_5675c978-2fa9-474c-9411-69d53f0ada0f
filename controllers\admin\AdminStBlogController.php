<?php
/**
 * ST Smart Blog - Admin Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/../../classes/StBlogPost.php';
require_once dirname(__FILE__) . '/../../classes/StBlogCategory.php';
require_once dirname(__FILE__) . '/../../classes/StBlogComment.php';
require_once dirname(__FILE__) . '/../../classes/StBlogTag.php';

class AdminStBlogController extends ModuleAdminController
{
    public function __construct()
    {
        $this->bootstrap = true;
        $this->table = 'stblog_post';
        $this->className = 'StBlogPost';
        $this->lang = true;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->addRowAction('view');

        $this->bulk_actions = [
            'delete' => [
                'text' => $this->l('Delete selected'),
                'icon' => 'icon-trash',
                'confirm' => $this->l('Delete selected items?')
            ]
        ];

        $this->fields_list = [
            'id_stblog_post' => [
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ],
            'title' => [
                'title' => $this->l('Title'),
                'width' => 'auto',
                'filter_key' => 'b!title'
            ],
            'category_name' => [
                'title' => $this->l('Category'),
                'width' => 140,
                'filter_key' => 'cl!name'
            ],
            'author_name' => [
                'title' => $this->l('Author'),
                'width' => 120,
                'filter_key' => 'e!firstname'
            ],
            'views' => [
                'title' => $this->l('Views'),
                'width' => 70,
                'align' => 'center'
            ],
            'comments_count' => [
                'title' => $this->l('Comments'),
                'width' => 70,
                'align' => 'center'
            ],
            'featured' => [
                'title' => $this->l('Featured'),
                'width' => 70,
                'align' => 'center',
                'type' => 'bool',
                'active' => 'featured'
            ],
            'active' => [
                'title' => $this->l('Status'),
                'width' => 70,
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool'
            ],
            'date_publish' => [
                'title' => $this->l('Published'),
                'width' => 120,
                'type' => 'datetime'
            ]
        ];

        parent::__construct();
    }

    public function renderList()
    {
        $this->addRowActionSkipList('view', []);

        return parent::renderList();
    }

    public function getList($id_lang, $order_by = null, $order_way = null, $start = 0, $limit = null, $id_lang_shop = false)
    {
        parent::getList($id_lang, $order_by, $order_way, $start, $limit, $id_lang_shop);

        // Add additional data to each row
        foreach ($this->_list as &$row) {
            // Get category name
            $category = new StBlogCategory($row['id_category'], $id_lang);
            $row['category_name'] = $category->name;

            // Get author name
            if ($row['id_employee']) {
                $employee = new Employee($row['id_employee']);
                $row['author_name'] = $employee->firstname . ' ' . $employee->lastname;
            } else {
                $row['author_name'] = $this->l('Unknown');
            }

            // Get comments count
            $row['comments_count'] = StBlogComment::getCommentsCount($row['id_stblog_post']);
        }
    }

    public function renderForm()
    {
        $categories = StBlogCategory::getCategories($this->context->language->id, false);
        $categories_options = [];
        foreach ($categories as $category) {
            $categories_options[] = [
                'id_option' => $category['id_stblog_category'],
                'name' => $category['name']
            ];
        }

        $employees = Employee::getEmployees();
        $employees_options = [];
        foreach ($employees as $employee) {
            $employees_options[] = [
                'id_option' => $employee['id_employee'],
                'name' => $employee['firstname'] . ' ' . $employee['lastname']
            ];
        }

        $this->fields_form = [
            'legend' => [
                'title' => $this->l('Blog Post'),
                'icon' => 'icon-edit'
            ],
            'input' => [
                [
                    'type' => 'text',
                    'label' => $this->l('Title'),
                    'name' => 'title',
                    'lang' => true,
                    'required' => true,
                    'col' => 6
                ],
                [
                    'type' => 'text',
                    'label' => $this->l('Link rewrite'),
                    'name' => 'link_rewrite',
                    'lang' => true,
                    'required' => true,
                    'col' => 6,
                    'hint' => $this->l('Only letters, numbers, underscore (_) and the minus (-) character are allowed.')
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Category'),
                    'name' => 'id_category',
                    'required' => true,
                    'options' => [
                        'query' => $categories_options,
                        'id' => 'id_option',
                        'name' => 'name'
                    ],
                    'col' => 3
                ],
                [
                    'type' => 'select',
                    'label' => $this->l('Author'),
                    'name' => 'id_employee',
                    'options' => [
                        'query' => $employees_options,
                        'id' => 'id_option',
                        'name' => 'name'
                    ],
                    'col' => 3
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Short content'),
                    'name' => 'content_short',
                    'lang' => true,
                    'rows' => 5,
                    'cols' => 40,
                    'hint' => $this->l('Brief description of the post for listings.')
                ],
                [
                    'type' => 'textarea',
                    'label' => $this->l('Content'),
                    'name' => 'content',
                    'lang' => true,
                    'autoload_rte' => true,
                    'rows' => 10,
                    'cols' => 40,
                    'hint' => $this->l('Full content of the blog post.')
                ],
                [
                    'type' => 'datetime',
                    'label' => $this->l('Publication date'),
                    'name' => 'date_publish',
                    'col' => 4
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Featured'),
                    'name' => 'featured',
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'featured_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ],
                        [
                            'id' => 'featured_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        ]
                    ]
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Allow comments'),
                    'name' => 'allow_comments',
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'allow_comments_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ],
                        [
                            'id' => 'allow_comments_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        ]
                    ]
                ],
                [
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'is_bool' => true,
                    'values' => [
                        [
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ],
                        [
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        ]
                    ]
                ]
            ]
        ];

        // Add SEO tab
        $this->fields_form['input'][] = [
            'type' => 'text',
            'label' => $this->l('Meta title'),
            'name' => 'meta_title',
            'lang' => true,
            'col' => 6,
            'tab' => 'seo'
        ];

        $this->fields_form['input'][] = [
            'type' => 'textarea',
            'label' => $this->l('Meta description'),
            'name' => 'meta_description',
            'lang' => true,
            'rows' => 3,
            'cols' => 40,
            'tab' => 'seo'
        ];

        $this->fields_form['input'][] = [
            'type' => 'text',
            'label' => $this->l('Meta keywords'),
            'name' => 'meta_keywords',
            'lang' => true,
            'col' => 6,
            'tab' => 'seo'
        ];

        $this->fields_form['submit'] = [
            'title' => $this->l('Save')
        ];

        return parent::renderForm();
    }

    public function processSave()
    {
        $object = parent::processSave();

        if ($object && Tools::isSubmit('submitAddstblog_post')) {
            // Handle tags
            $tags = Tools::getValue('tags');
            if ($tags) {
                $tags_array = explode(',', $tags);
                StBlogTag::setTagsForPost($object->id, $tags_array, $this->context->language->id);
            }
        }

        return $object;
    }

    public function initToolbar()
    {
        parent::initToolbar();

        $this->page_header_toolbar_btn['new'] = [
            'href' => self::$currentIndex . '&add' . $this->table . '&token=' . $this->token,
            'desc' => $this->l('Add new post'),
            'icon' => 'process-icon-new'
        ];
    }
}

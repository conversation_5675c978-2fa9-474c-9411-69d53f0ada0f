{*
* ST Smart Blog - Right Column Hook Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{* Search Widget *}
<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Search Blog' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <form action="{$blog_url}" method="get" class="blog-search-form" id="stblog-search-form">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="{l s='Search posts...' mod='stsmartblog'}" value="{if isset($search_query)}{$search_query|escape:'html':'UTF-8'}{/if}">
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

{* Recent Posts Widget *}
{if $recent_posts && count($recent_posts) > 0}
<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Recent Posts' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        {foreach from=$recent_posts item=post}
            <div class="recent-post-item">
                <h4>
                    <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                        {$post.title|truncate:45:'...'}
                    </a>
                </h4>
                <div class="post-meta">
                    <span class="post-date">
                        <i class="fa fa-calendar"></i>
                        {$post.date_formatted}
                    </span>
                    <span class="post-views">
                        <i class="fa fa-eye"></i>
                        {$post.views}
                    </span>
                </div>
            </div>
        {/foreach}
    </div>
</div>
{/if}

{* Archive Widget *}
{if $archive_months && count($archive_months) > 0}
<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Archive' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <ul class="archive-list">
            {foreach from=$archive_months item=month}
                <li>
                    <a href="{$month.url}" title="{$month.label|escape:'html':'UTF-8'}">
                        {$month.label}
                        <span class="post-count">({$month.post_count})</span>
                    </a>
                </li>
            {/foreach}
        </ul>
    </div>
</div>
{/if}

{* Newsletter Widget *}
{if $show_newsletter}
<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Newsletter' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <p>{l s='Subscribe to our newsletter to get the latest updates.' mod='stsmartblog'}</p>
        <form action="{$newsletter_url}" method="post" class="newsletter-form" id="stblog-newsletter-form">
            <div class="form-group">
                <input type="email" name="email" class="form-control" placeholder="{l s='Your email address' mod='stsmartblog'}" required>
            </div>
            <button type="submit" class="btn btn-primary btn-block">
                {l s='Subscribe' mod='stsmartblog'}
            </button>
        </form>
    </div>
</div>
{/if}

{* Social Links Widget *}
{if $social_links && count($social_links) > 0}
<div class="stblog-sidebar-widget">
    <h3 class="widget-title">
        {l s='Follow Us' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <div class="social-links">
            {foreach from=$social_links item=link}
                <a href="{$link.url}" target="_blank" class="social-link social-{$link.type}" title="{$link.title|escape:'html':'UTF-8'}">
                    <i class="fa fa-{$link.icon}"></i>
                </a>
            {/foreach}
        </div>
    </div>
</div>
{/if}

<style>
.stblog-sidebar-widget {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 5px;
    padding: 20px;
    margin-bottom: 30px;
}

.stblog-sidebar-widget .widget-title {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 2px solid #007bff;
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.stblog-sidebar-widget .widget-content {
    color: #555;
}

.blog-search-form .input-group {
    margin-bottom: 0;
}

.blog-search-form .form-control {
    border-right: none;
}

.blog-search-form .btn {
    border-left: none;
}

.recent-post-item {
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ddd;
}

.recent-post-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.recent-post-item h4 {
    margin-bottom: 5px;
    font-size: 14px;
    line-height: 1.4;
}

.recent-post-item a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.recent-post-item a:hover {
    color: #007bff;
}

.recent-post-item .post-meta {
    color: #666;
    font-size: 12px;
}

.recent-post-item .post-meta span {
    margin-right: 10px;
}

.recent-post-item .post-meta i {
    margin-right: 3px;
}

.archive-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.archive-list li {
    margin-bottom: 8px;
    padding: 5px 0;
}

.archive-list a {
    color: #333;
    text-decoration: none;
    display: block;
    transition: color 0.3s ease;
}

.archive-list a:hover {
    color: #007bff;
}

.post-count {
    color: #666;
    font-size: 12px;
    float: right;
}

.newsletter-form .form-group {
    margin-bottom: 15px;
}

.newsletter-form .form-control {
    border-radius: 4px;
}

.newsletter-form .btn {
    border-radius: 4px;
}

.social-links {
    text-align: center;
}

.social-link {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    margin: 0 5px 10px 0;
    border-radius: 50%;
    color: white;
    text-decoration: none;
    transition: transform 0.3s ease, opacity 0.3s ease;
}

.social-link:hover {
    transform: translateY(-2px);
    opacity: 0.8;
    text-decoration: none;
    color: white;
}

.social-facebook { background: #3b5998; }
.social-twitter { background: #1da1f2; }
.social-instagram { background: #e4405f; }
.social-linkedin { background: #0077b5; }
.social-youtube { background: #ff0000; }
.social-pinterest { background: #bd081c; }

@media (max-width: 768px) {
    .stblog-sidebar-widget {
        padding: 15px;
        margin-bottom: 20px;
    }
    
    .post-count {
        float: none;
        display: inline;
    }
    
    .recent-post-item .post-meta span {
        display: block;
        margin-bottom: 3px;
        margin-right: 0;
    }
    
    .social-link {
        width: 35px;
        height: 35px;
        line-height: 35px;
    }
}
</style>

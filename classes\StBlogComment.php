<?php
/**
 * ST Smart Blog - Blog Comment Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StBlogComment extends ObjectModel
{
    /** @var int */
    public $id_stblog_comment;

    /** @var int */
    public $id_stblog_post;

    /** @var int */
    public $id_customer;

    /** @var int */
    public $id_parent;

    /** @var string */
    public $author_name;

    /** @var string */
    public $author_email;

    /** @var string */
    public $author_website;

    /** @var string */
    public $content;

    /** @var bool */
    public $active;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'stblog_comment',
        'primary' => 'id_stblog_comment',
        'fields' => [
            'id_stblog_post' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId', 'required' => true],
            'id_customer' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'id_parent' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'author_name' => ['type' => self::TYPE_STRING, 'validate' => 'isGenericName', 'required' => true, 'size' => 255],
            'author_email' => ['type' => self::TYPE_STRING, 'validate' => 'isEmail', 'required' => true, 'size' => 255],
            'author_website' => ['type' => self::TYPE_STRING, 'validate' => 'isUrl', 'size' => 255],
            'content' => ['type' => self::TYPE_HTML, 'validate' => 'isCleanHtml', 'required' => true],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    public function add($autodate = true, $null_values = false)
    {
        // Auto-approve if moderation is disabled
        if (!Configuration::get('STBLOG_MODERATE_COMMENTS')) {
            $this->active = 1;
        }

        $result = parent::add($autodate, $null_values);

        if ($result && Configuration::get('STBLOG_MODERATE_COMMENTS')) {
            $this->sendModerationNotification();
        }

        return $result;
    }

    /**
     * Get comments by post
     */
    public static function getCommentsByPost($id_post, $active = true, $limit = null)
    {
        $sql = 'SELECT c.*, cu.firstname, cu.lastname
                FROM `' . _DB_PREFIX_ . 'stblog_comment` c
                LEFT JOIN `' . _DB_PREFIX_ . 'customer` cu ON (c.id_customer = cu.id_customer)
                WHERE c.id_stblog_post = ' . (int)$id_post;

        if ($active) {
            $sql .= ' AND c.active = 1';
        }

        $sql .= ' ORDER BY c.date_add ASC';

        if ($limit) {
            $sql .= ' LIMIT ' . (int)$limit;
        }

        $comments = Db::getInstance()->executeS($sql);

        // Build threaded comments
        return self::buildCommentTree($comments);
    }

    /**
     * Build comment tree for threaded comments
     */
    private static function buildCommentTree($comments, $parent_id = 0)
    {
        $tree = [];

        foreach ($comments as $comment) {
            if ($comment['id_parent'] == $parent_id) {
                $comment['children'] = self::buildCommentTree($comments, $comment['id_stblog_comment']);
                $tree[] = $comment;
            }
        }

        return $tree;
    }

    /**
     * Get total comments count for post
     */
    public static function getCommentsCount($id_post, $active = true)
    {
        $sql = 'SELECT COUNT(*)
                FROM `' . _DB_PREFIX_ . 'stblog_comment`
                WHERE id_stblog_post = ' . (int)$id_post;

        if ($active) {
            $sql .= ' AND active = 1';
        }

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Get latest comments
     */
    public static function getLatestComments($limit = 10, $active = true)
    {
        $sql = 'SELECT c.*, p.id_stblog_post, pl.title as post_title, pl.link_rewrite as post_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_comment` c
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post` p ON (c.id_stblog_post = p.id_stblog_post)
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_post_lang` pl ON (p.id_stblog_post = pl.id_stblog_post)
                WHERE pl.id_lang = ' . (int)Context::getContext()->language->id . '
                AND pl.id_shop = ' . (int)Context::getContext()->shop->id;

        if ($active) {
            $sql .= ' AND c.active = 1 AND p.active = 1';
        }

        $sql .= ' ORDER BY c.date_add DESC
                  LIMIT ' . (int)$limit;

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Approve comment
     */
    public function approve()
    {
        $this->active = 1;
        return $this->update();
    }

    /**
     * Reject comment
     */
    public function reject()
    {
        $this->active = 0;
        return $this->update();
    }

    /**
     * Send moderation notification email
     */
    private function sendModerationNotification()
    {
        $post = new StBlogPost($this->id_stblog_post, Context::getContext()->language->id);
        
        $template_vars = [
            'comment_author' => $this->author_name,
            'comment_content' => $this->content,
            'post_title' => $post->title,
            'comment_date' => $this->date_add,
            'admin_link' => Context::getContext()->link->getAdminLink('AdminStBlogComments')
        ];

        // Get admin emails
        $admin_emails = [];
        $employees = Employee::getEmployees();
        foreach ($employees as $employee) {
            if ($employee['active'] && $employee['email']) {
                $admin_emails[] = $employee['email'];
            }
        }

        foreach ($admin_emails as $email) {
            Mail::Send(
                Context::getContext()->language->id,
                'stblog_comment_moderation',
                'New comment awaiting moderation',
                $template_vars,
                $email,
                null,
                null,
                null,
                null,
                null,
                dirname(__FILE__) . '/../mails/',
                false,
                Context::getContext()->shop->id
            );
        }
    }

    /**
     * Get comment author display name
     */
    public function getAuthorDisplayName()
    {
        if ($this->id_customer) {
            $customer = new Customer($this->id_customer);
            if (Validate::isLoadedObject($customer)) {
                return $customer->firstname . ' ' . $customer->lastname;
            }
        }

        return $this->author_name;
    }

    /**
     * Get comment author avatar
     */
    public function getAuthorAvatar()
    {
        if ($this->id_customer) {
            // Return customer avatar if available
            // This would need to be implemented based on your avatar system
            return '';
        }

        // Return Gravatar for guest comments
        $hash = md5(strtolower(trim($this->author_email)));
        return 'https://www.gravatar.com/avatar/' . $hash . '?s=50&d=identicon';
    }

    /**
     * Check if comment can be replied to
     */
    public function canReply()
    {
        // Limit reply depth to prevent infinite nesting
        $max_depth = 3;
        $current_depth = $this->getDepth();
        
        return $current_depth < $max_depth;
    }

    /**
     * Get comment depth in thread
     */
    private function getDepth($depth = 0)
    {
        if ($this->id_parent == 0) {
            return $depth;
        }

        $parent = new StBlogComment($this->id_parent);
        return $parent->getDepth($depth + 1);
    }

    /**
     * Get replies to this comment
     */
    public function getReplies($active = true)
    {
        $sql = 'SELECT c.*, cu.firstname, cu.lastname
                FROM `' . _DB_PREFIX_ . 'stblog_comment` c
                LEFT JOIN `' . _DB_PREFIX_ . 'customer` cu ON (c.id_customer = cu.id_customer)
                WHERE c.id_parent = ' . (int)$this->id;

        if ($active) {
            $sql .= ' AND c.active = 1';
        }

        $sql .= ' ORDER BY c.date_add ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Validate comment content for spam
     */
    public function validateContent()
    {
        // Basic spam detection
        $spam_words = ['spam', 'viagra', 'casino', 'poker'];
        $content_lower = strtolower($this->content);

        foreach ($spam_words as $word) {
            if (strpos($content_lower, $word) !== false) {
                return false;
            }
        }

        // Check for excessive links
        $link_count = substr_count($content_lower, 'http');
        if ($link_count > 2) {
            return false;
        }

        return true;
    }

    /**
     * Get pending comments count
     */
    public static function getPendingCommentsCount()
    {
        $sql = 'SELECT COUNT(*)
                FROM `' . _DB_PREFIX_ . 'stblog_comment`
                WHERE active = 0';

        return (int)Db::getInstance()->getValue($sql);
    }
}

{*
* ST Smart Blog - Tag Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{extends file='page.tpl'}

{block name='page_title'}
    {l s='Tag:' mod='stsmartblog'} {$tag.name}
{/block}

{block name='page_content'}
<div class="stblog-wrapper">
    <div class="row">
        <div class="col-lg-8 col-md-12">
            <div class="tag-header">
                <h1 class="tag-title">
                    <i class="fa fa-tag"></i>
                    {l s='Tag:' mod='stsmartblog'} {$tag.name}
                </h1>
                {if $tag.description}
                    <div class="tag-description">
                        {$tag.description nofilter}
                    </div>
                {/if}
                
                <div class="tag-meta">
                    <span class="post-count">
                        <i class="fa fa-file-text"></i>
                        {$posts_count} {if $posts_count == 1}{l s='post' mod='stsmartblog'}{else}{l s='posts' mod='stsmartblog'}{/if}
                    </span>
                </div>
            </div>
            
            {if $posts && count($posts) > 0}
                <div class="posts-section">
                    {foreach from=$posts item=post}
                        <article class="stblog-post-item">
                            <div class="post-header">
                                <h2 class="post-title">
                                    <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                                        {$post.title}
                                    </a>
                                </h2>
                                
                                <div class="post-meta">
                                    <span class="post-date">
                                        <i class="fa fa-calendar"></i>
                                        {$post.date_formatted}
                                    </span>
                                    <span class="post-author">
                                        <i class="fa fa-user"></i>
                                        {l s='By' mod='stsmartblog'} {$post.author_name|default:'Admin'}
                                    </span>
                                    <span class="post-views">
                                        <i class="fa fa-eye"></i>
                                        {$post.views} {l s='views' mod='stsmartblog'}
                                    </span>
                                    {if $post.category_title}
                                    <span class="post-category">
                                        <i class="fa fa-folder"></i>
                                        <a href="{$post.category_url}" title="{$post.category_title|escape:'html':'UTF-8'}">
                                            {$post.category_title}
                                        </a>
                                    </span>
                                    {/if}
                                    {if $post.comments_count > 0}
                                    <span class="post-comments">
                                        <i class="fa fa-comments"></i>
                                        <a href="{$post.url}#comments">
                                            {$post.comments_count} {if $post.comments_count == 1}{l s='comment' mod='stsmartblog'}{else}{l s='comments' mod='stsmartblog'}{/if}
                                        </a>
                                    </span>
                                    {/if}
                                </div>
                            </div>
                            
                            <div class="post-content">
                                {if $post.content_short}
                                    {$post.content_short|strip_tags|truncate:300:'...'}
                                {else}
                                    {$post.content|strip_tags|truncate:300:'...'}
                                {/if}
                            </div>
                            
                            <div class="post-footer">
                                <a href="{$post.url}" class="btn btn-primary btn-sm">
                                    {l s='Read More' mod='stsmartblog'}
                                </a>
                                
                                {if $post.tags && count($post.tags) > 0}
                                    <div class="post-tags">
                                        {foreach from=$post.tags item=post_tag name=tags}
                                            <a href="{$post_tag.url}" class="tag-item {if $post_tag.name == $tag.name}current-tag{/if}" title="{$post_tag.name|escape:'html':'UTF-8'}">
                                                {$post_tag.name}
                                            </a>
                                            {if !$smarty.foreach.tags.last}, {/if}
                                        {/foreach}
                                    </div>
                                {/if}
                            </div>
                        </article>
                    {/foreach}
                    
                    {if $pagination}
                        <div class="stblog-pagination">
                            {$pagination nofilter}
                        </div>
                    {/if}
                </div>
            {else}
                <div class="no-posts-message">
                    <div class="text-center">
                        <i class="fa fa-tag fa-3x text-muted mb-3"></i>
                        <h4>{l s='No posts found' mod='stsmartblog'}</h4>
                        <p class="text-muted">
                            {l s='There are currently no posts with this tag.' mod='stsmartblog'}
                        </p>
                        <a href="{$blog_url}" class="btn btn-primary">
                            {l s='Browse All Posts' mod='stsmartblog'}
                        </a>
                    </div>
                </div>
            {/if}
        </div>
        
        <div class="col-lg-4 col-md-12">
            <aside class="stblog-sidebar">
                {include file='module:stsmartblog/views/templates/front/sidebar.tpl'}
            </aside>
        </div>
    </div>
</div>

<style>
.tag-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 2px solid #eee;
}

.tag-title {
    margin-bottom: 15px;
    font-size: 32px;
    font-weight: 600;
    color: #333;
}

.tag-title i {
    margin-right: 10px;
    color: #007bff;
}

.tag-description {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
    font-size: 16px;
}

.tag-meta {
    color: #666;
    font-size: 14px;
}

.tag-meta i {
    margin-right: 5px;
}

.posts-section {
    margin-bottom: 30px;
}

.stblog-post-item {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.stblog-post-item:last-child {
    border-bottom: none;
}

.post-header {
    margin-bottom: 15px;
}

.post-title {
    margin-bottom: 10px;
    font-size: 24px;
    font-weight: 600;
}

.post-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.post-title a:hover {
    color: #007bff;
}

.post-meta {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

.post-meta span {
    margin-right: 15px;
    display: inline-block;
}

.post-meta i {
    margin-right: 5px;
}

.post-meta a {
    color: #666;
    text-decoration: none;
}

.post-meta a:hover {
    color: #007bff;
}

.post-content {
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}

.post-footer {
    margin-top: 15px;
}

.post-tags {
    margin-top: 10px;
}

.tag-item {
    display: inline-block;
    margin: 0 5px 5px 0;
    padding: 3px 8px;
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
    border-radius: 3px;
    font-size: 12px;
    transition: background-color 0.3s ease;
}

.tag-item:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

.tag-item.current-tag {
    background: #007bff;
    color: white;
}

.no-posts-message {
    padding: 60px 20px;
    text-align: center;
}

.no-posts-message .fa {
    color: #dee2e6;
}

.no-posts-message h4 {
    margin-bottom: 15px;
    color: #6c757d;
}

.no-posts-message p {
    margin-bottom: 25px;
}

.stblog-pagination {
    margin-top: 30px;
    text-align: center;
}

.stblog-pagination .pagination {
    justify-content: center;
}

@media (max-width: 768px) {
    .tag-title {
        font-size: 28px;
    }
    
    .tag-description {
        font-size: 15px;
    }
    
    .post-title {
        font-size: 20px;
    }
    
    .post-meta span {
        display: block;
        margin-bottom: 5px;
        margin-right: 0;
    }
    
    .no-posts-message {
        padding: 40px 15px;
    }
}

@media (max-width: 576px) {
    .tag-title {
        font-size: 24px;
    }
    
    .post-title {
        font-size: 18px;
    }
}
</style>
{/block}

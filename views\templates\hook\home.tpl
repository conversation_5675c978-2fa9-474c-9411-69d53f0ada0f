{*
* ST Smart Blog - Home Hook Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{if $latest_posts && count($latest_posts) > 0}
<section class="stblog-home-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h2 class="section-title">
                    {l s='Latest Blog Posts' mod='stsmartblog'}
                </h2>
                <p class="section-subtitle">
                    {l s='Stay updated with our latest news and articles' mod='stsmartblog'}
                </p>
            </div>
        </div>
        
        <div class="row">
            {foreach from=$latest_posts item=post}
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <article class="stblog-home-post">
                        <div class="post-header">
                            <h3 class="post-title">
                                <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                                    {$post.title|truncate:60:'...'}
                                </a>
                            </h3>
                            <div class="post-meta">
                                <span class="post-date">
                                    <i class="fa fa-calendar"></i>
                                    {$post.date_formatted}
                                </span>
                                <span class="post-views">
                                    <i class="fa fa-eye"></i>
                                    {$post.views}
                                </span>
                            </div>
                        </div>
                        
                        <div class="post-content">
                            {if $post.content_short}
                                <p>{$post.content_short|strip_tags|truncate:120:'...'}</p>
                            {else}
                                <p>{$post.content|strip_tags|truncate:120:'...'}</p>
                            {/if}
                        </div>
                        
                        <div class="post-footer">
                            <a href="{$post.url}" class="btn btn-primary btn-sm">
                                {l s='Read More' mod='stsmartblog'}
                            </a>
                        </div>
                    </article>
                </div>
            {/foreach}
        </div>
        
        <div class="row">
            <div class="col-12 text-center">
                <a href="{$blog_url}" class="btn btn-outline-primary">
                    {l s='View All Posts' mod='stsmartblog'}
                </a>
            </div>
        </div>
    </div>
</section>

<style>
.stblog-home-section {
    padding: 40px 0;
    background: #f8f9fa;
    margin: 30px 0;
}

.section-title {
    text-align: center;
    margin-bottom: 10px;
    font-size: 28px;
    font-weight: 600;
    color: #333;
}

.section-subtitle {
    text-align: center;
    margin-bottom: 40px;
    color: #666;
    font-size: 16px;
}

.stblog-home-post {
    background: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 30px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.stblog-home-post:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 20px rgba(0,0,0,0.15);
}

.stblog-home-post .post-title {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.stblog-home-post .post-title a {
    color: #333;
    text-decoration: none;
}

.stblog-home-post .post-title a:hover {
    color: #007bff;
}

.stblog-home-post .post-meta {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

.stblog-home-post .post-meta span {
    margin-right: 15px;
}

.stblog-home-post .post-meta i {
    margin-right: 5px;
}

.stblog-home-post .post-content {
    flex-grow: 1;
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}

.stblog-home-post .post-footer {
    margin-top: auto;
}

@media (max-width: 768px) {
    .stblog-home-section {
        padding: 30px 0;
    }
    
    .section-title {
        font-size: 24px;
    }
    
    .stblog-home-post {
        padding: 15px;
    }
}
</style>
{/if}

{*
* ST Smart Blog - Admin Configuration Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="panel">
    <div class="panel-heading">
        <i class="icon-cogs"></i>
        {l s='ST Smart Blog Configuration' mod='stsmartblog'}
    </div>
    
    <div class="panel-body">
        <div class="row">
            <div class="col-lg-12">
                <div class="alert alert-info">
                    <p><strong>{l s='Welcome to ST Smart Blog!' mod='stsmartblog'}</strong></p>
                    <p>{l s='This module provides a complete blogging solution for your PrestaShop store. Configure the settings below to get started.' mod='stsmartblog'}</p>
                </div>
            </div>
        </div>
        
        {if isset($confirmation)}
            <div class="alert alert-success">
                {$confirmation}
            </div>
        {/if}
        
        {if isset($errors) && count($errors)}
            <div class="alert alert-danger">
                <ul>
                    {foreach from=$errors item=error}
                        <li>{$error}</li>
                    {/foreach}
                </ul>
            </div>
        {/if}
        
        <form id="configuration_form" class="defaultForm form-horizontal" action="{$form_action}" method="post" enctype="multipart/form-data">
            
            {* General Settings *}
            <div class="panel">
                <div class="panel-heading">
                    <i class="icon-cog"></i>
                    {l s='General Settings' mod='stsmartblog'}
                </div>
                <div class="panel-body">
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Enable Blog' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <span class="switch prestashop-switch fixed-width-lg">
                                <input type="radio" name="STBLOG_LIVE_MODE" id="STBLOG_LIVE_MODE_on" value="1" {if $STBLOG_LIVE_MODE}checked="checked"{/if}>
                                <label for="STBLOG_LIVE_MODE_on">{l s='Yes' mod='stsmartblog'}</label>
                                <input type="radio" name="STBLOG_LIVE_MODE" id="STBLOG_LIVE_MODE_off" value="0" {if !$STBLOG_LIVE_MODE}checked="checked"{/if}>
                                <label for="STBLOG_LIVE_MODE_off">{l s='No' mod='stsmartblog'}</label>
                                <a class="slide-button btn"></a>
                            </span>
                            <p class="help-block">{l s='Enable or disable the blog module.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Blog Title' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <input type="text" name="STBLOG_META_TITLE" value="{$STBLOG_META_TITLE|escape:'html':'UTF-8'}" class="form-control">
                            <p class="help-block">{l s='The title of your blog (used in meta tags and navigation).' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Blog Description' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <textarea name="STBLOG_META_DESCRIPTION" class="form-control" rows="3">{$STBLOG_META_DESCRIPTION|escape:'html':'UTF-8'}</textarea>
                            <p class="help-block">{l s='A brief description of your blog (used in meta tags).' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Posts per Page' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <input type="number" name="STBLOG_POSTS_PER_PAGE" value="{$STBLOG_POSTS_PER_PAGE|escape:'html':'UTF-8'}" class="form-control" min="1" max="50">
                            <p class="help-block">{l s='Number of posts to display per page.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            {* Display Settings *}
            <div class="panel">
                <div class="panel-heading">
                    <i class="icon-eye"></i>
                    {l s='Display Settings' mod='stsmartblog'}
                </div>
                <div class="panel-body">
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Show in Top Menu' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <span class="switch prestashop-switch fixed-width-lg">
                                <input type="radio" name="STBLOG_SHOW_TOP_LINK" id="STBLOG_SHOW_TOP_LINK_on" value="1" {if $STBLOG_SHOW_TOP_LINK}checked="checked"{/if}>
                                <label for="STBLOG_SHOW_TOP_LINK_on">{l s='Yes' mod='stsmartblog'}</label>
                                <input type="radio" name="STBLOG_SHOW_TOP_LINK" id="STBLOG_SHOW_TOP_LINK_off" value="0" {if !$STBLOG_SHOW_TOP_LINK}checked="checked"{/if}>
                                <label for="STBLOG_SHOW_TOP_LINK_off">{l s='No' mod='stsmartblog'}</label>
                                <a class="slide-button btn"></a>
                            </span>
                            <p class="help-block">{l s='Display blog link in the top menu.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Show on Homepage' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <span class="switch prestashop-switch fixed-width-lg">
                                <input type="radio" name="STBLOG_SHOW_HOME_POSTS" id="STBLOG_SHOW_HOME_POSTS_on" value="1" {if $STBLOG_SHOW_HOME_POSTS}checked="checked"{/if}>
                                <label for="STBLOG_SHOW_HOME_POSTS_on">{l s='Yes' mod='stsmartblog'}</label>
                                <input type="radio" name="STBLOG_SHOW_HOME_POSTS" id="STBLOG_SHOW_HOME_POSTS_off" value="0" {if !$STBLOG_SHOW_HOME_POSTS}checked="checked"{/if}>
                                <label for="STBLOG_SHOW_HOME_POSTS_off">{l s='No' mod='stsmartblog'}</label>
                                <a class="slide-button btn"></a>
                            </span>
                            <p class="help-block">{l s='Display latest blog posts on the homepage.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Homepage Posts Count' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <input type="number" name="STBLOG_HOME_POSTS_COUNT" value="{$STBLOG_HOME_POSTS_COUNT|escape:'html':'UTF-8'}" class="form-control" min="1" max="12">
                            <p class="help-block">{l s='Number of posts to display on homepage.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Show in Footer' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <span class="switch prestashop-switch fixed-width-lg">
                                <input type="radio" name="STBLOG_SHOW_FOOTER_POSTS" id="STBLOG_SHOW_FOOTER_POSTS_on" value="1" {if $STBLOG_SHOW_FOOTER_POSTS}checked="checked"{/if}>
                                <label for="STBLOG_SHOW_FOOTER_POSTS_on">{l s='Yes' mod='stsmartblog'}</label>
                                <input type="radio" name="STBLOG_SHOW_FOOTER_POSTS" id="STBLOG_SHOW_FOOTER_POSTS_off" value="0" {if !$STBLOG_SHOW_FOOTER_POSTS}checked="checked"{/if}>
                                <label for="STBLOG_SHOW_FOOTER_POSTS_off">{l s='No' mod='stsmartblog'}</label>
                                <a class="slide-button btn"></a>
                            </span>
                            <p class="help-block">{l s='Display latest blog posts in the footer.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            {* Comment Settings *}
            <div class="panel">
                <div class="panel-heading">
                    <i class="icon-comments"></i>
                    {l s='Comment Settings' mod='stsmartblog'}
                </div>
                <div class="panel-body">
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Enable Comments' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <span class="switch prestashop-switch fixed-width-lg">
                                <input type="radio" name="STBLOG_ENABLE_COMMENTS" id="STBLOG_ENABLE_COMMENTS_on" value="1" {if $STBLOG_ENABLE_COMMENTS}checked="checked"{/if}>
                                <label for="STBLOG_ENABLE_COMMENTS_on">{l s='Yes' mod='stsmartblog'}</label>
                                <input type="radio" name="STBLOG_ENABLE_COMMENTS" id="STBLOG_ENABLE_COMMENTS_off" value="0" {if !$STBLOG_ENABLE_COMMENTS}checked="checked"{/if}>
                                <label for="STBLOG_ENABLE_COMMENTS_off">{l s='No' mod='stsmartblog'}</label>
                                <a class="slide-button btn"></a>
                            </span>
                            <p class="help-block">{l s='Allow visitors to comment on blog posts.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Moderate Comments' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <span class="switch prestashop-switch fixed-width-lg">
                                <input type="radio" name="STBLOG_MODERATE_COMMENTS" id="STBLOG_MODERATE_COMMENTS_on" value="1" {if $STBLOG_MODERATE_COMMENTS}checked="checked"{/if}>
                                <label for="STBLOG_MODERATE_COMMENTS_on">{l s='Yes' mod='stsmartblog'}</label>
                                <input type="radio" name="STBLOG_MODERATE_COMMENTS" id="STBLOG_MODERATE_COMMENTS_off" value="0" {if !$STBLOG_MODERATE_COMMENTS}checked="checked"{/if}>
                                <label for="STBLOG_MODERATE_COMMENTS_off">{l s='No' mod='stsmartblog'}</label>
                                <a class="slide-button btn"></a>
                            </span>
                            <p class="help-block">{l s='Require admin approval before comments are published.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            {* SEO Settings *}
            <div class="panel">
                <div class="panel-heading">
                    <i class="icon-search"></i>
                    {l s='SEO Settings' mod='stsmartblog'}
                </div>
                <div class="panel-body">
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Blog URL Rewrite' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <input type="text" name="STBLOG_URL_REWRITE" value="{$STBLOG_URL_REWRITE|escape:'html':'UTF-8'}" class="form-control">
                            <p class="help-block">{l s='URL slug for the blog (e.g., "blog", "news", "articles").' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label class="control-label col-lg-3">
                            {l s='Generate Sitemap' mod='stsmartblog'}
                        </label>
                        <div class="col-lg-9">
                            <span class="switch prestashop-switch fixed-width-lg">
                                <input type="radio" name="STBLOG_GENERATE_SITEMAP" id="STBLOG_GENERATE_SITEMAP_on" value="1" {if $STBLOG_GENERATE_SITEMAP}checked="checked"{/if}>
                                <label for="STBLOG_GENERATE_SITEMAP_on">{l s='Yes' mod='stsmartblog'}</label>
                                <input type="radio" name="STBLOG_GENERATE_SITEMAP" id="STBLOG_GENERATE_SITEMAP_off" value="0" {if !$STBLOG_GENERATE_SITEMAP}checked="checked"{/if}>
                                <label for="STBLOG_GENERATE_SITEMAP_off">{l s='No' mod='stsmartblog'}</label>
                                <a class="slide-button btn"></a>
                            </span>
                            <p class="help-block">{l s='Automatically generate XML sitemap for blog posts.' mod='stsmartblog'}</p>
                        </div>
                    </div>
                    
                </div>
            </div>
            
            <div class="panel-footer">
                <button type="submit" value="1" id="configuration_form_submit_btn" name="submitStBlogConfiguration" class="btn btn-default pull-right">
                    <i class="process-icon-save"></i> {l s='Save' mod='stsmartblog'}
                </button>
            </div>
            
        </form>
    </div>
</div>

{* Quick Actions *}
<div class="panel">
    <div class="panel-heading">
        <i class="icon-bolt"></i>
        {l s='Quick Actions' mod='stsmartblog'}
    </div>
    <div class="panel-body">
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="quick-action-card">
                    <div class="card-icon">
                        <i class="icon-plus"></i>
                    </div>
                    <h4>{l s='Create Post' mod='stsmartblog'}</h4>
                    <p>{l s='Write a new blog post' mod='stsmartblog'}</p>
                    <a href="{$admin_blog_url}&addstblog_post" class="btn btn-primary btn-sm">
                        {l s='New Post' mod='stsmartblog'}
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="quick-action-card">
                    <div class="card-icon">
                        <i class="icon-folder"></i>
                    </div>
                    <h4>{l s='Manage Categories' mod='stsmartblog'}</h4>
                    <p>{l s='Organize your content' mod='stsmartblog'}</p>
                    <a href="{$admin_categories_url}" class="btn btn-primary btn-sm">
                        {l s='Categories' mod='stsmartblog'}
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="quick-action-card">
                    <div class="card-icon">
                        <i class="icon-comments"></i>
                    </div>
                    <h4>{l s='Moderate Comments' mod='stsmartblog'}</h4>
                    <p>{l s='Review pending comments' mod='stsmartblog'}</p>
                    <a href="{$admin_comments_url}" class="btn btn-primary btn-sm">
                        {l s='Comments' mod='stsmartblog'}
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="quick-action-card">
                    <div class="card-icon">
                        <i class="icon-eye"></i>
                    </div>
                    <h4>{l s='View Blog' mod='stsmartblog'}</h4>
                    <p>{l s='See your blog in action' mod='stsmartblog'}</p>
                    <a href="{$blog_url}" class="btn btn-primary btn-sm" target="_blank">
                        {l s='Visit Blog' mod='stsmartblog'}
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.quick-action-card {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    text-align: center;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.quick-action-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.quick-action-card .card-icon {
    font-size: 32px;
    color: #007bff;
    margin-bottom: 15px;
}

.quick-action-card h4 {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
}

.quick-action-card p {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}
</style>

<?php
/**
 * ST Smart Blog - Post Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/../../classes/StBlogPost.php';
require_once dirname(__FILE__) . '/../../classes/StBlogCategory.php';
require_once dirname(__FILE__) . '/../../classes/StBlogComment.php';
require_once dirname(__FILE__) . '/../../classes/StBlogTag.php';

class StSmartBlogPostModuleFrontController extends ModuleFrontController
{
    public $ssl = true;
    public $post;

    public function __construct()
    {
        parent::__construct();
        $this->context = Context::getContext();
    }

    public function init()
    {
        parent::init();

        $id_post = (int)Tools::getValue('id_post');
        $post_rewrite = Tools::getValue('post_rewrite');
        $id_lang = $this->context->language->id;
        $id_shop = $this->context->shop->id;

        // Get post by ID or rewrite
        if ($id_post) {
            $this->post = new StBlogPost($id_post, $id_lang, $id_shop);
        } elseif ($post_rewrite) {
            $this->post = StBlogPost::getPostByRewrite($post_rewrite, $id_lang, $id_shop);
        }

        // Check if post exists and is active
        if (!Validate::isLoadedObject($this->post) || !$this->post->active) {
            Tools::redirect('index.php?controller=404');
        }

        // Check publication date
        if ($this->post->date_publish > date('Y-m-d H:i:s')) {
            Tools::redirect('index.php?controller=404');
        }

        // Increment views
        $this->post->incrementViews();
    }

    public function initContent()
    {
        parent::initContent();

        $id_lang = $this->context->language->id;
        $id_shop = $this->context->shop->id;

        // Get post category
        $category = new StBlogCategory($this->post->id_category, $id_lang, $id_shop);

        // Get post author
        $author = null;
        if ($this->post->id_employee) {
            $author = new Employee($this->post->id_employee);
        }

        // Get post tags
        $tags = StBlogTag::getTagsByPost($this->post->id, $id_lang, $id_shop);

        // Get comments if enabled
        $comments = [];
        $comments_count = 0;
        if ($this->post->allow_comments && Configuration::get('STBLOG_ENABLE_COMMENTS')) {
            $comments = StBlogComment::getCommentsByPost($this->post->id, true);
            $comments_count = StBlogComment::getCommentsCount($this->post->id, true);
        }

        // Get related posts
        $related_posts = StBlogPost::getLatestPosts($id_lang, 4, $id_shop);
        // Remove current post from related posts
        $related_posts = array_filter($related_posts, function($post) {
            return $post['id_stblog_post'] != $this->post->id;
        });
        $related_posts = array_slice($related_posts, 0, 3);

        // Prepare related posts URLs
        foreach ($related_posts as &$related_post) {
            $related_post['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'post',
                ['id_post' => $related_post['id_stblog_post'], 'post_rewrite' => $related_post['link_rewrite']]
            );
        }

        // Prepare tags URLs
        foreach ($tags as &$tag) {
            $tag['url'] = $this->context->link->getModuleLink(
                'stsmartblog',
                'tag',
                ['id_tag' => $tag['id_stblog_tag'], 'tag_rewrite' => $tag['link_rewrite']]
            );
        }

        // Social sharing URLs
        $post_url = $this->context->link->getModuleLink(
            'stsmartblog',
            'post',
            ['id_post' => $this->post->id, 'post_rewrite' => $this->post->link_rewrite]
        );

        $social_share = [];
        if (Configuration::get('STBLOG_ENABLE_SOCIAL_SHARE')) {
            $social_share = [
                'facebook' => 'https://www.facebook.com/sharer/sharer.php?u=' . urlencode($post_url),
                'twitter' => 'https://twitter.com/intent/tweet?url=' . urlencode($post_url) . '&text=' . urlencode($this->post->title),
                'linkedin' => 'https://www.linkedin.com/sharing/share-offsite/?url=' . urlencode($post_url),
                'pinterest' => 'https://pinterest.com/pin/create/button/?url=' . urlencode($post_url) . '&description=' . urlencode($this->post->title),
                'whatsapp' => 'https://wa.me/?text=' . urlencode($this->post->title . ' ' . $post_url)
            ];
        }

        // Breadcrumb
        $breadcrumb = $category->getBreadcrumb($id_lang, $id_shop);

        $this->context->smarty->assign([
            'post' => $this->post,
            'category' => $category,
            'author' => $author,
            'tags' => $tags,
            'comments' => $comments,
            'comments_count' => $comments_count,
            'related_posts' => $related_posts,
            'social_share' => $social_share,
            'breadcrumb' => $breadcrumb,
            'post_url' => $post_url,
            'category_url' => $this->context->link->getModuleLink(
                'stsmartblog',
                'category',
                ['id_category' => $category->id, 'category_rewrite' => $category->link_rewrite]
            ),
            'blog_url' => $this->context->link->getModuleLink('stsmartblog', 'list'),
            'module_dir' => $this->module->getPathUri(),
            'allow_comments' => $this->post->allow_comments && Configuration::get('STBLOG_ENABLE_COMMENTS'),
            'moderate_comments' => Configuration::get('STBLOG_MODERATE_COMMENTS'),
            'date_formatted' => Tools::displayDate($this->post->date_publish, $id_lang)
        ]);

        $this->setTemplate('module:stsmartblog/views/templates/front/post.tpl');
    }

    public function postProcess()
    {
        // Handle comment submission
        if (Tools::isSubmit('submitComment') && $this->post->allow_comments && Configuration::get('STBLOG_ENABLE_COMMENTS')) {
            $this->processComment();
        }
    }

    protected function processComment()
    {
        $errors = [];

        $author_name = Tools::getValue('author_name');
        $author_email = Tools::getValue('author_email');
        $author_website = Tools::getValue('author_website');
        $content = Tools::getValue('content');
        $id_parent = (int)Tools::getValue('id_parent');

        // Validation
        if (empty($author_name)) {
            $errors[] = $this->l('Name is required');
        }

        if (empty($author_email) || !Validate::isEmail($author_email)) {
            $errors[] = $this->l('Valid email is required');
        }

        if (empty($content)) {
            $errors[] = $this->l('Comment content is required');
        }

        if (!empty($author_website) && !Validate::isUrl($author_website)) {
            $errors[] = $this->l('Invalid website URL');
        }

        if (empty($errors)) {
            $comment = new StBlogComment();
            $comment->id_stblog_post = $this->post->id;
            $comment->id_customer = $this->context->customer->isLogged() ? $this->context->customer->id : null;
            $comment->id_parent = $id_parent;
            $comment->author_name = $author_name;
            $comment->author_email = $author_email;
            $comment->author_website = $author_website;
            $comment->content = $content;

            // Validate content for spam
            if (!$comment->validateContent()) {
                $errors[] = $this->l('Comment contains inappropriate content');
            } else {
                if ($comment->add()) {
                    if (Configuration::get('STBLOG_MODERATE_COMMENTS')) {
                        $this->context->smarty->assign('comment_success', $this->l('Your comment has been submitted and is awaiting moderation.'));
                    } else {
                        $this->context->smarty->assign('comment_success', $this->l('Your comment has been posted successfully.'));
                    }
                } else {
                    $errors[] = $this->l('Error saving comment');
                }
            }
        }

        if (!empty($errors)) {
            $this->context->smarty->assign('comment_errors', $errors);
        }
    }

    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();

        // Add blog link
        $breadcrumb['links'][] = [
            'title' => $this->l('Blog'),
            'url' => $this->context->link->getModuleLink('stsmartblog', 'list')
        ];

        // Add category breadcrumb
        $category = new StBlogCategory($this->post->id_category, $this->context->language->id, $this->context->shop->id);
        $category_breadcrumb = $category->getBreadcrumb($this->context->language->id, $this->context->shop->id);
        
        foreach ($category_breadcrumb as $cat) {
            $breadcrumb['links'][] = [
                'title' => $cat['name'],
                'url' => $this->context->link->getModuleLink(
                    'stsmartblog',
                    'category',
                    ['id_category' => $cat['id'], 'category_rewrite' => $cat['link_rewrite']]
                )
            ];
        }

        // Add current post
        $breadcrumb['links'][] = [
            'title' => $this->post->title,
            'url' => $this->context->link->getModuleLink(
                'stsmartblog',
                'post',
                ['id_post' => $this->post->id, 'post_rewrite' => $this->post->link_rewrite]
            )
        ];

        return $breadcrumb;
    }

    public function getCanonicalURL()
    {
        return $this->context->link->getModuleLink(
            'stsmartblog',
            'post',
            ['id_post' => $this->post->id, 'post_rewrite' => $this->post->link_rewrite]
        );
    }

    public function getTemplateVarPage()
    {
        $page = parent::getTemplateVarPage();
        
        $page['meta']['title'] = !empty($this->post->meta_title) ? $this->post->meta_title : $this->post->title;
        $page['meta']['description'] = !empty($this->post->meta_description) ? $this->post->meta_description : $this->post->content_short;
        $page['meta']['keywords'] = $this->post->meta_keywords;
        $page['meta']['robots'] = 'index,follow';
        
        // Add canonical URL
        $page['canonical'] = $this->getCanonicalURL();
        
        // Add Open Graph tags
        $page['meta']['og:title'] = $page['meta']['title'];
        $page['meta']['og:description'] = $page['meta']['description'];
        $page['meta']['og:type'] = 'article';
        $page['meta']['og:url'] = $this->getCanonicalURL();
        $page['meta']['article:published_time'] = $this->post->date_publish;
        $page['meta']['article:modified_time'] = $this->post->date_upd;

        return $page;
    }

    public function l($string, $specific = false, $locale = null)
    {
        return Translate::getModuleTranslation('stsmartblog', $string, 'post', $locale);
    }
}

<?php
/**
 * ST Smart Blog - Admin Tags Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class AdminStBlogTagsController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'stblog_tag';
        $this->className = 'StBlogTag';
        $this->lang = true;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
        $this->bulk_actions = array(
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?')
            )
        );

        $this->bootstrap = true;
        $this->context = Context::getContext();

        parent::__construct();

        $this->meta_title = $this->l('Blog Tags');

        $this->fields_list = array(
            'id_stblog_tag' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'name' => array(
                'title' => $this->l('Name'),
                'width' => 'auto',
                'filter_key' => 'b!name'
            ),
            'description' => array(
                'title' => $this->l('Description'),
                'width' => 300,
                'maxlength' => 100,
                'orderby' => false,
                'search' => false,
                'callback' => 'getDescriptionClean'
            ),
            'post_count' => array(
                'title' => $this->l('Posts'),
                'width' => 70,
                'align' => 'center',
                'orderby' => false,
                'search' => false,
                'callback' => 'getPostCount'
            ),
            'active' => array(
                'title' => $this->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ),
            'date_add' => array(
                'title' => $this->l('Date'),
                'width' => 120,
                'type' => 'datetime',
                'align' => 'right'
            )
        );

        $this->_orderBy = 'name';
        $this->_orderWay = 'ASC';
    }

    public function getDescriptionClean($description)
    {
        return Tools::truncateString(strip_tags($description), 100);
    }

    public function getPostCount($value, $row)
    {
        $count = Db::getInstance()->getValue('
            SELECT COUNT(*)
            FROM `'._DB_PREFIX_.'stblog_post_tag` pt
            INNER JOIN `'._DB_PREFIX_.'stblog_post` p ON (p.id_stblog_post = pt.id_stblog_post)
            WHERE pt.id_stblog_tag = '.(int)$row['id_stblog_tag'].'
            AND p.active = 1
        ');
        
        return (int)$count;
    }

    public function renderForm()
    {
        $this->fields_form = array(
            'legend' => array(
                'title' => $this->l('Tag'),
                'icon' => 'icon-tag'
            ),
            'input' => array(
                array(
                    'type' => 'text',
                    'label' => $this->l('Name'),
                    'name' => 'name',
                    'lang' => true,
                    'required' => true,
                    'col' => 6,
                    'hint' => $this->l('Tag name (e.g., "Technology", "News", "Tips")')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'lang' => true,
                    'rows' => 5,
                    'col' => 6,
                    'hint' => $this->l('Optional description for this tag')
                ),
                array(
                    'type' => 'text',
                    'label' => $this->l('Meta title'),
                    'name' => 'meta_title',
                    'lang' => true,
                    'col' => 6,
                    'hint' => $this->l('Meta title for SEO (leave empty to use tag name)')
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Meta description'),
                    'name' => 'meta_description',
                    'lang' => true,
                    'rows' => 3,
                    'col' => 6,
                    'hint' => $this->l('Meta description for SEO')
                ),
                array(
                    'type' => 'tags',
                    'label' => $this->l('Meta keywords'),
                    'name' => 'meta_keywords',
                    'lang' => true,
                    'col' => 6,
                    'hint' => $this->l('Meta keywords for SEO, separated by commas')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        )
                    )
                )
            ),
            'submit' => array(
                'title' => $this->l('Save')
            )
        );

        if (!($tag = $this->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    public function renderList()
    {
        $this->addRowActionSkipList('delete', array());
        
        return parent::renderList();
    }

    public function postProcess()
    {
        if (Tools::isSubmit('submitAdd'.$this->table)) {
            // Auto-generate meta_title if empty
            foreach (Language::getLanguages(false) as $language) {
                $name = Tools::getValue('name_'.$language['id_lang']);
                $meta_title = Tools::getValue('meta_title_'.$language['id_lang']);
                
                if (empty($meta_title) && !empty($name)) {
                    $_POST['meta_title_'.$language['id_lang']] = $name;
                }
            }
        }

        return parent::postProcess();
    }

    public function processBulkDelete()
    {
        if (is_array($this->boxes) && !empty($this->boxes)) {
            $object = new $this->className();
            
            if ($this->access('delete')) {
                $result = true;
                foreach ($this->boxes as $id) {
                    $to_delete = new $this->className((int)$id);
                    
                    // Check if tag is used by posts
                    $post_count = Db::getInstance()->getValue('
                        SELECT COUNT(*)
                        FROM `'._DB_PREFIX_.'stblog_post_tag`
                        WHERE id_stblog_tag = '.(int)$id
                    );
                    
                    if ($post_count > 0) {
                        $this->errors[] = sprintf(
                            $this->l('Cannot delete tag "%s" because it is used by %d post(s).'),
                            $to_delete->name,
                            $post_count
                        );
                        $result = false;
                    } else {
                        $result &= $to_delete->delete();
                    }
                }
                
                if ($result) {
                    $this->confirmations[] = $this->l('The selection has been successfully deleted.');
                }
            } else {
                $this->errors[] = Tools::displayError('You do not have permission to delete this.');
            }
        } else {
            $this->errors[] = Tools::displayError('You must select at least one element to delete.');
        }
    }

    public function processDelete()
    {
        if (Validate::isLoadedObject($object = $this->loadObject())) {
            // Check if tag is used by posts
            $post_count = Db::getInstance()->getValue('
                SELECT COUNT(*)
                FROM `'._DB_PREFIX_.'stblog_post_tag`
                WHERE id_stblog_tag = '.(int)$object->id
            );
            
            if ($post_count > 0) {
                $this->errors[] = sprintf(
                    $this->l('Cannot delete tag "%s" because it is used by %d post(s).'),
                    $object->name,
                    $post_count
                );
            } else {
                return parent::processDelete();
            }
        } else {
            $this->errors[] = Tools::displayError('An error occurred while loading the object.');
        }
    }

    public function initToolbar()
    {
        parent::initToolbar();
        
        // Add custom toolbar buttons
        $this->toolbar_btn['import'] = array(
            'href' => self::$currentIndex.'&import_tags&token='.$this->token,
            'desc' => $this->l('Import Tags'),
            'icon' => 'process-icon-upload'
        );
        
        $this->toolbar_btn['export'] = array(
            'href' => self::$currentIndex.'&export_tags&token='.$this->token,
            'desc' => $this->l('Export Tags'),
            'icon' => 'process-icon-download'
        );
    }

    public function processImportTags()
    {
        // Handle tag import functionality
        if (isset($_FILES['import_file']) && $_FILES['import_file']['error'] == 0) {
            $file_content = file_get_contents($_FILES['import_file']['tmp_name']);
            $tags = explode("\n", $file_content);
            
            $imported = 0;
            foreach ($tags as $tag_name) {
                $tag_name = trim($tag_name);
                if (!empty($tag_name)) {
                    $existing = Db::getInstance()->getValue('
                        SELECT id_stblog_tag 
                        FROM `'._DB_PREFIX_.'stblog_tag_lang` 
                        WHERE name = "'.pSQL($tag_name).'"
                        AND id_lang = '.(int)$this->context->language->id
                    );
                    
                    if (!$existing) {
                        $tag = new StBlogTag();
                        $tag->name = array($this->context->language->id => $tag_name);
                        $tag->active = 1;
                        if ($tag->add()) {
                            $imported++;
                        }
                    }
                }
            }
            
            $this->confirmations[] = sprintf($this->l('%d tags imported successfully.'), $imported);
        }
    }

    public function processExportTags()
    {
        $tags = StBlogTag::getTags($this->context->language->id);
        
        $content = '';
        foreach ($tags as $tag) {
            $content .= $tag['name']."\n";
        }
        
        header('Content-Type: text/plain');
        header('Content-Disposition: attachment; filename="blog_tags_export.txt"');
        echo $content;
        exit;
    }
}

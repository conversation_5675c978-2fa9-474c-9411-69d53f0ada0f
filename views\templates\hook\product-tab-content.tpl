{*
* ST Smart Blog - Product Tab Content Hook Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

<div class="tab-pane fade" id="stblog-content" role="tabpanel" aria-labelledby="stblog-tab">
    <div class="stblog-product-tab-content">
        {if $related_posts && count($related_posts) > 0}
            <h3 class="tab-title">
                {l s='Related Articles' mod='stsmartblog'}
            </h3>
            <p class="tab-description">
                {l s='Discover articles related to this product' mod='stsmartblog'}
            </p>
            
            <div class="row">
                {foreach from=$related_posts item=post}
                    <div class="col-lg-6 col-md-12">
                        <article class="related-post-item">
                            <div class="post-header">
                                <h4 class="post-title">
                                    <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                                        {$post.title|truncate:60:'...'}
                                    </a>
                                </h4>
                                <div class="post-meta">
                                    <span class="post-date">
                                        <i class="fa fa-calendar"></i>
                                        {$post.date_formatted}
                                    </span>
                                    <span class="post-views">
                                        <i class="fa fa-eye"></i>
                                        {$post.views}
                                    </span>
                                </div>
                            </div>
                            
                            <div class="post-content">
                                {if $post.content_short}
                                    <p>{$post.content_short|strip_tags|truncate:100:'...'}</p>
                                {else}
                                    <p>{$post.content|strip_tags|truncate:100:'...'}</p>
                                {/if}
                            </div>
                            
                            <div class="post-footer">
                                <a href="{$post.url}" class="btn btn-primary btn-sm">
                                    {l s='Read More' mod='stsmartblog'}
                                </a>
                                {if $post.tags && count($post.tags) > 0}
                                    <div class="post-tags">
                                        {foreach from=$post.tags item=tag name=tags}
                                            <a href="{$tag.url}" class="tag-item" title="{$tag.name|escape:'html':'UTF-8'}">
                                                {$tag.name}
                                            </a>
                                            {if !$smarty.foreach.tags.last}, {/if}
                                        {/foreach}
                                    </div>
                                {/if}
                            </div>
                        </article>
                    </div>
                {/foreach}
            </div>
            
            <div class="text-center mt-4">
                <a href="{$blog_url}" class="btn btn-outline-primary">
                    {l s='View All Articles' mod='stsmartblog'}
                </a>
            </div>
        {else}
            <div class="no-posts-message">
                <div class="text-center">
                    <i class="fa fa-newspaper-o fa-3x text-muted mb-3"></i>
                    <h4>{l s='No Related Articles' mod='stsmartblog'}</h4>
                    <p class="text-muted">
                        {l s='There are currently no articles related to this product.' mod='stsmartblog'}
                    </p>
                    <a href="{$blog_url}" class="btn btn-primary">
                        {l s='Browse All Articles' mod='stsmartblog'}
                    </a>
                </div>
            </div>
        {/if}
    </div>
</div>

<style>
.stblog-product-tab-content {
    padding: 20px 0;
}

.tab-title {
    margin-bottom: 10px;
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.tab-description {
    margin-bottom: 30px;
    color: #666;
    font-size: 16px;
}

.related-post-item {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.related-post-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

.related-post-item .post-title {
    margin-bottom: 10px;
    font-size: 18px;
    font-weight: 600;
}

.related-post-item .post-title a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s ease;
}

.related-post-item .post-title a:hover {
    color: #007bff;
}

.related-post-item .post-meta {
    margin-bottom: 15px;
    color: #666;
    font-size: 14px;
}

.related-post-item .post-meta span {
    margin-right: 15px;
}

.related-post-item .post-meta i {
    margin-right: 5px;
}

.related-post-item .post-content {
    flex-grow: 1;
    margin-bottom: 15px;
    line-height: 1.6;
    color: #555;
}

.related-post-item .post-footer {
    margin-top: auto;
}

.related-post-item .post-tags {
    margin-top: 10px;
}

.related-post-item .tag-item {
    display: inline-block;
    margin: 0 3px 3px 0;
    padding: 2px 6px;
    background: #e9ecef;
    color: #495057;
    text-decoration: none;
    border-radius: 3px;
    font-size: 11px;
    transition: background-color 0.3s ease;
}

.related-post-item .tag-item:hover {
    background: #007bff;
    color: white;
    text-decoration: none;
}

.no-posts-message {
    padding: 40px 20px;
    text-align: center;
}

.no-posts-message .fa {
    color: #dee2e6;
}

.no-posts-message h4 {
    margin-bottom: 15px;
    color: #6c757d;
}

.no-posts-message p {
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .stblog-product-tab-content {
        padding: 15px 0;
    }
    
    .tab-title {
        font-size: 20px;
    }
    
    .related-post-item {
        padding: 15px;
    }
    
    .related-post-item .post-meta span {
        display: block;
        margin-bottom: 5px;
        margin-right: 0;
    }
    
    .no-posts-message {
        padding: 30px 15px;
    }
}

@media (max-width: 576px) {
    .related-post-item .post-title {
        font-size: 16px;
    }
    
    .tab-description {
        font-size: 14px;
    }
}
</style>

<?php
/**
 * ST Smart Blog - Professional PrestaShop Blog Module
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 * @version   1.0.0
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

require_once dirname(__FILE__) . '/classes/StBlogPost.php';
require_once dirname(__FILE__) . '/classes/StBlogCategory.php';
require_once dirname(__FILE__) . '/classes/StBlogComment.php';
require_once dirname(__FILE__) . '/classes/StBlogTag.php';

class StSmartBlog extends Module
{
    protected $config_form = false;

    public function __construct()
    {
        $this->name = 'stsmartblog';
        $this->tab = 'content_management';
        $this->version = '1.0.0';
        $this->author = 'Sathi';
        $this->need_instance = 0;
        $this->ps_versions_compliancy = [
            'min' => '*******',
            'max' => '9.0.99'
        ];
        $this->bootstrap = true;

        parent::__construct();

        $this->displayName = $this->l('Smart Blog');
        $this->description = $this->l('Professional blogging solution for PrestaShop with SEO optimization, multilingual support, and advanced features.');
        $this->confirmUninstall = $this->l('Are you sure you want to uninstall? All blog data will be lost.');

        $this->dependencies = [];
    }

    /**
     * Don't forget to create update methods if needed:
     * http://doc.prestashop.com/display/PS16/Enabling+the+Auto-Update
     */
    public function install()
    {
        Configuration::updateValue('STBLOG_LIVE_MODE', false);
        Configuration::updateValue('STBLOG_POSTS_PER_PAGE', 10);
        Configuration::updateValue('STBLOG_ENABLE_COMMENTS', true);
        Configuration::updateValue('STBLOG_MODERATE_COMMENTS', true);
        Configuration::updateValue('STBLOG_ENABLE_SOCIAL_SHARE', true);
        Configuration::updateValue('STBLOG_SEO_URLS', true);
        Configuration::updateValue('STBLOG_ENABLE_SITEMAP', true);
        Configuration::updateValue('STBLOG_CHATGPT_API_KEY', '');
        Configuration::updateValue('STBLOG_FACEBOOK_APP_ID', '');

        return parent::install() &&
            $this->registerHook('header') &&
            $this->registerHook('backOfficeHeader') &&
            $this->registerHook('displayNav') &&
            $this->registerHook('displayTop') &&
            $this->registerHook('displayHome') &&
            $this->registerHook('displayFooter') &&
            $this->registerHook('displayLeftColumn') &&
            $this->registerHook('displayRightColumn') &&
            $this->registerHook('displayProductTab') &&
            $this->registerHook('displayProductTabContent') &&
            $this->registerHook('moduleRoutes') &&
            $this->installDB() &&
            $this->installTabs() &&
            $this->installDemoContent();
    }

    public function uninstall()
    {
        Configuration::deleteByName('STBLOG_LIVE_MODE');
        Configuration::deleteByName('STBLOG_POSTS_PER_PAGE');
        Configuration::deleteByName('STBLOG_ENABLE_COMMENTS');
        Configuration::deleteByName('STBLOG_MODERATE_COMMENTS');
        Configuration::deleteByName('STBLOG_ENABLE_SOCIAL_SHARE');
        Configuration::deleteByName('STBLOG_SEO_URLS');
        Configuration::deleteByName('STBLOG_ENABLE_SITEMAP');
        Configuration::deleteByName('STBLOG_CHATGPT_API_KEY');
        Configuration::deleteByName('STBLOG_FACEBOOK_APP_ID');

        return parent::uninstall() &&
            $this->uninstallDB() &&
            $this->uninstallTabs();
    }

    /**
     * Install database tables
     */
    protected function installDB()
    {
        $sql = [];

        // Blog posts table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_post` (
            `id_stblog_post` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `id_category` int(10) unsigned NOT NULL DEFAULT 1,
            `id_employee` int(10) unsigned DEFAULT NULL,
            `position` int(10) unsigned NOT NULL DEFAULT 0,
            `active` tinyint(1) unsigned NOT NULL DEFAULT 0,
            `featured` tinyint(1) unsigned NOT NULL DEFAULT 0,
            `allow_comments` tinyint(1) unsigned NOT NULL DEFAULT 1,
            `views` int(10) unsigned NOT NULL DEFAULT 0,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            `date_publish` datetime DEFAULT NULL,
            PRIMARY KEY (`id_stblog_post`),
            KEY `id_category` (`id_category`),
            KEY `id_employee` (`id_employee`),
            KEY `active` (`active`),
            KEY `featured` (`featured`),
            KEY `date_publish` (`date_publish`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Blog post lang table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_post_lang` (
            `id_stblog_post` int(10) unsigned NOT NULL,
            `id_lang` int(10) unsigned NOT NULL,
            `id_shop` int(10) unsigned NOT NULL DEFAULT 1,
            `title` varchar(255) NOT NULL,
            `content` longtext,
            `content_short` text,
            `meta_title` varchar(255) DEFAULT NULL,
            `meta_description` varchar(512) DEFAULT NULL,
            `meta_keywords` varchar(255) DEFAULT NULL,
            `link_rewrite` varchar(128) NOT NULL,
            PRIMARY KEY (`id_stblog_post`, `id_lang`, `id_shop`),
            KEY `id_lang` (`id_lang`),
            KEY `id_shop` (`id_shop`),
            KEY `link_rewrite` (`link_rewrite`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Blog categories table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_category` (
            `id_stblog_category` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `id_parent` int(10) unsigned NOT NULL DEFAULT 0,
            `position` int(10) unsigned NOT NULL DEFAULT 0,
            `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_stblog_category`),
            KEY `id_parent` (`id_parent`),
            KEY `active` (`active`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Blog category lang table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_category_lang` (
            `id_stblog_category` int(10) unsigned NOT NULL,
            `id_lang` int(10) unsigned NOT NULL,
            `id_shop` int(10) unsigned NOT NULL DEFAULT 1,
            `name` varchar(255) NOT NULL,
            `description` text,
            `meta_title` varchar(255) DEFAULT NULL,
            `meta_description` varchar(512) DEFAULT NULL,
            `meta_keywords` varchar(255) DEFAULT NULL,
            `link_rewrite` varchar(128) NOT NULL,
            PRIMARY KEY (`id_stblog_category`, `id_lang`, `id_shop`),
            KEY `id_lang` (`id_lang`),
            KEY `id_shop` (`id_shop`),
            KEY `link_rewrite` (`link_rewrite`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        foreach ($sql as $query) {
            if (Db::getInstance()->execute($query) == false) {
                return false;
            }
        }

        return $this->installDBPart2();
    }

    /**
     * Install database tables - Part 2
     */
    protected function installDBPart2()
    {
        $sql = [];

        // Blog comments table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_comment` (
            `id_stblog_comment` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `id_stblog_post` int(10) unsigned NOT NULL,
            `id_customer` int(10) unsigned DEFAULT NULL,
            `id_parent` int(10) unsigned NOT NULL DEFAULT 0,
            `author_name` varchar(255) NOT NULL,
            `author_email` varchar(255) NOT NULL,
            `author_website` varchar(255) DEFAULT NULL,
            `content` text NOT NULL,
            `active` tinyint(1) unsigned NOT NULL DEFAULT 0,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_stblog_comment`),
            KEY `id_stblog_post` (`id_stblog_post`),
            KEY `id_customer` (`id_customer`),
            KEY `id_parent` (`id_parent`),
            KEY `active` (`active`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Blog tags table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_tag` (
            `id_stblog_tag` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_stblog_tag`),
            KEY `active` (`active`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Blog tag lang table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_tag_lang` (
            `id_stblog_tag` int(10) unsigned NOT NULL,
            `id_lang` int(10) unsigned NOT NULL,
            `id_shop` int(10) unsigned NOT NULL DEFAULT 1,
            `name` varchar(255) NOT NULL,
            `meta_title` varchar(255) DEFAULT NULL,
            `meta_description` varchar(512) DEFAULT NULL,
            `meta_keywords` varchar(255) DEFAULT NULL,
            `link_rewrite` varchar(128) NOT NULL,
            PRIMARY KEY (`id_stblog_tag`, `id_lang`, `id_shop`),
            KEY `id_lang` (`id_lang`),
            KEY `id_shop` (`id_shop`),
            KEY `link_rewrite` (`link_rewrite`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Post-Tag association table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_post_tag` (
            `id_stblog_post` int(10) unsigned NOT NULL,
            `id_stblog_tag` int(10) unsigned NOT NULL,
            PRIMARY KEY (`id_stblog_post`, `id_stblog_tag`),
            KEY `id_stblog_post` (`id_stblog_post`),
            KEY `id_stblog_tag` (`id_stblog_tag`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Post-Product association table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_post_product` (
            `id_stblog_post` int(10) unsigned NOT NULL,
            `id_product` int(10) unsigned NOT NULL,
            PRIMARY KEY (`id_stblog_post`, `id_product`),
            KEY `id_stblog_post` (`id_stblog_post`),
            KEY `id_product` (`id_product`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        // Redirect management table
        $sql[] = 'CREATE TABLE IF NOT EXISTS `' . _DB_PREFIX_ . 'stblog_redirect` (
            `id_stblog_redirect` int(10) unsigned NOT NULL AUTO_INCREMENT,
            `old_url` varchar(255) NOT NULL,
            `new_url` varchar(255) NOT NULL,
            `type` enum("301","302") NOT NULL DEFAULT "301",
            `active` tinyint(1) unsigned NOT NULL DEFAULT 1,
            `date_add` datetime NOT NULL,
            `date_upd` datetime NOT NULL,
            PRIMARY KEY (`id_stblog_redirect`),
            KEY `old_url` (`old_url`),
            KEY `active` (`active`)
        ) ENGINE=' . _MYSQL_ENGINE_ . ' DEFAULT CHARSET=utf8;';

        foreach ($sql as $query) {
            if (Db::getInstance()->execute($query) == false) {
                return false;
            }
        }

        return $this->createDefaultCategory();
    }

    /**
     * Create default blog category
     */
    protected function createDefaultCategory()
    {
        $category = new StBlogCategory();
        $category->id_parent = 0;
        $category->position = 1;
        $category->active = 1;

        $languages = Language::getLanguages(false);
        foreach ($languages as $language) {
            $category->name[$language['id_lang']] = 'General';
            $category->description[$language['id_lang']] = 'Default blog category';
            $category->meta_title[$language['id_lang']] = 'General';
            $category->meta_description[$language['id_lang']] = 'General blog category';
            $category->link_rewrite[$language['id_lang']] = 'general';
        }

        return $category->add();
    }

    /**
     * Uninstall database tables
     */
    protected function uninstallDB()
    {
        $sql = [];
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_post_lang`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_post`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_category_lang`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_category`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_comment`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_tag_lang`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_tag`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_post_tag`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_post_product`';
        $sql[] = 'DROP TABLE IF EXISTS `' . _DB_PREFIX_ . 'stblog_redirect`';

        foreach ($sql as $query) {
            if (Db::getInstance()->execute($query) == false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Install admin tabs
     */
    protected function installTabs()
    {
        $tab = new Tab();
        $tab->active = 1;
        $tab->class_name = 'AdminStBlog';
        $tab->name = [];
        foreach (Language::getLanguages(true) as $lang) {
            $tab->name[$lang['id_lang']] = 'ST Smart Blog';
        }
        $tab->id_parent = (int)Tab::getIdFromClassName('IMPROVE');
        $tab->module = $this->name;
        return $tab->add();
    }

    /**
     * Uninstall admin tabs
     */
    protected function uninstallTabs()
    {
        $id_tab = (int)Tab::getIdFromClassName('AdminStBlog');
        if ($id_tab) {
            $tab = new Tab($id_tab);
            return $tab->delete();
        }
        return true;
    }

    /**
     * Install demo content
     */
    protected function installDemoContent()
    {
        // This will be implemented later
        return true;
    }

    /**
     * Load the configuration form
     */
    public function getContent()
    {
        /**
         * If values have been submitted in the form, process.
         */
        if (((bool)Tools::isSubmit('submitStSmartBlogModule')) == true) {
            $this->postProcess();
        }

        $this->context->smarty->assign('module_dir', $this->_path);

        $output = $this->context->smarty->fetch($this->local_path.'views/templates/admin/configure.tpl');

        return $output.$this->renderForm();
    }

    /**
     * Create the form that will be displayed in the configuration of your module.
     */
    protected function renderForm()
    {
        $helper = new HelperForm();

        $helper->show_toolbar = false;
        $helper->table = $this->table;
        $helper->module = $this;
        $helper->default_form_language = $this->context->language->id;
        $helper->allow_employee_form_lang = Configuration::get('PS_BO_ALLOW_EMPLOYEE_FORM_LANG', 0);

        $helper->identifier = $this->identifier;
        $helper->submit_action = 'submitStSmartBlogModule';
        $helper->currentIndex = $this->context->link->getAdminLink('AdminModules', false)
            .'&configure='.$this->name.'&tab_module='.$this->tab.'&module_name='.$this->name;
        $helper->token = Tools::getAdminTokenLite('AdminModules');

        $helper->tpl_vars = [
            'fields_value' => $this->getConfigFormValues(),
            'languages' => $this->context->controller->getLanguages(),
            'id_language' => $this->context->language->id,
        ];

        return $helper->generateForm([$this->getConfigForm()]);
    }

    /**
     * Create the structure of your form.
     */
    protected function getConfigForm()
    {
        return [
            'form' => [
                'legend' => [
                'title' => $this->l('Settings'),
                'icon' => 'icon-cogs',
                ],
                'input' => [
                    [
                        'type' => 'switch',
                        'label' => $this->l('Live mode'),
                        'name' => 'STBLOG_LIVE_MODE',
                        'is_bool' => true,
                        'desc' => $this->l('Use this module in live mode'),
                        'values' => [
                            [
                                'id' => 'active_on',
                                'value' => true,
                                'label' => $this->l('Enabled')
                            ],
                            [
                                'id' => 'active_off',
                                'value' => false,
                                'label' => $this->l('Disabled')
                            ]
                        ],
                    ],
                ],
                'submit' => [
                    'title' => $this->l('Save'),
                ],
            ],
        ];
    }

    /**
     * Set values for the inputs.
     */
    protected function getConfigFormValues()
    {
        return [
            'STBLOG_LIVE_MODE' => Configuration::get('STBLOG_LIVE_MODE', true),
        ];
    }

    /**
     * Save form data.
     */
    protected function postProcess()
    {
        $form_values = $this->getConfigFormValues();

        foreach (array_keys($form_values) as $key) {
            Configuration::updateValue($key, Tools::getValue($key));
        }
    }

    /**
     * Add the CSS & JavaScript files you want to be loaded in the BO.
     */
    public function hookBackOfficeHeader()
    {
        if (Tools::getValue('module_name') == $this->name ||
            Tools::getValue('controller') == 'AdminStBlog' ||
            Tools::getValue('controller') == 'AdminStBlogCategories' ||
            Tools::getValue('controller') == 'AdminStBlogComments') {

            $this->context->controller->addJS($this->_path.'views/js/back.js');
            $this->context->controller->addCSS($this->_path.'views/css/back.css');
        }
    }

    /**
     * Add the CSS & JavaScript files you want to be added on the FO.
     */
    public function hookHeader()
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        $this->context->controller->addJS($this->_path.'/views/js/front.js');
        $this->context->controller->addCSS($this->_path.'/views/css/front.css');

        // Add structured data for blog posts
        if (Tools::getValue('controller') == 'post' && Tools::getValue('module') == 'stsmartblog') {
            $id_post = (int)Tools::getValue('id_post');
            if ($id_post) {
                $post = new StBlogPost($id_post, $this->context->language->id);
                if (Validate::isLoadedObject($post)) {
                    $structured_data = [
                        '@context' => 'https://schema.org',
                        '@type' => 'BlogPosting',
                        'headline' => $post->title,
                        'description' => $post->content_short,
                        'datePublished' => $post->date_publish,
                        'dateModified' => $post->date_upd,
                        'author' => [
                            '@type' => 'Person',
                            'name' => 'Blog Author'
                        ],
                        'publisher' => [
                            '@type' => 'Organization',
                            'name' => Configuration::get('PS_SHOP_NAME')
                        ]
                    ];

                    $this->context->smarty->assign('blog_structured_data', json_encode($structured_data));
                }
            }
        }
    }

    /**
     * Module routes for SEO URLs
     */
    public function hookModuleRoutes($params)
    {
        return [
            'stblog_list' => [
                'controller' => 'list',
                'rule' => 'blog',
                'keywords' => [],
                'params' => [
                    'fc' => 'module',
                    'module' => 'stsmartblog'
                ]
            ],
            'stblog_category' => [
                'controller' => 'category',
                'rule' => 'blog/category/{id}-{rewrite}',
                'keywords' => [
                    'id' => ['regexp' => '[0-9]+', 'param' => 'id_category'],
                    'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'category_rewrite'],
                ],
                'params' => [
                    'fc' => 'module',
                    'module' => 'stsmartblog'
                ]
            ],
            'stblog_post' => [
                'controller' => 'post',
                'rule' => 'blog/{id}-{rewrite}',
                'keywords' => [
                    'id' => ['regexp' => '[0-9]+', 'param' => 'id_post'],
                    'rewrite' => ['regexp' => '[_a-zA-Z0-9-\pL]*', 'param' => 'post_rewrite'],
                ],
                'params' => [
                    'fc' => 'module',
                    'module' => 'stsmartblog'
                ]
            ]
        ];
    }

    /**
     * Display blog posts in home page
     */
    public function hookDisplayHome($params)
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        $posts = StBlogPost::getLatestPosts($this->context->language->id, 3);

        $this->context->smarty->assign([
            'posts' => $posts,
            'blog_link' => $this->context->link->getModuleLink('stsmartblog', 'list')
        ]);

        return $this->display(__FILE__, 'views/templates/hook/home.tpl');
    }

    /**
     * Display blog navigation in top menu
     */
    public function hookDisplayNav($params)
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        $this->context->smarty->assign([
            'blog_link' => $this->context->link->getModuleLink('stsmartblog', 'list'),
            'blog_title' => $this->l('Blog')
        ]);

        return $this->display(__FILE__, 'views/templates/hook/nav.tpl');
    }

    /**
     * Display sidebar blocks
     */
    public function hookDisplayLeftColumn($params)
    {
        return $this->hookDisplayRightColumn($params);
    }

    public function hookDisplayRightColumn($params)
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        $latest_posts = StBlogPost::getLatestPosts($this->context->language->id, 5);
        $categories = StBlogCategory::getCategories($this->context->language->id);

        $this->context->smarty->assign([
            'latest_posts' => $latest_posts,
            'blog_categories' => $categories,
            'blog_link' => $this->context->link->getModuleLink('stsmartblog', 'list')
        ]);

        return $this->display(__FILE__, 'views/templates/hook/sidebar.tpl');
    }

    /**
     * Display related articles in product page
     */
    public function hookDisplayProductTab($params)
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        $id_product = (int)Tools::getValue('id_product');
        $related_posts = StBlogPost::getRelatedPosts($id_product, $this->context->language->id);

        if (empty($related_posts)) {
            return '';
        }

        return $this->display(__FILE__, 'views/templates/hook/product-tab.tpl');
    }

    public function hookDisplayProductTabContent($params)
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        $id_product = (int)Tools::getValue('id_product');
        $related_posts = StBlogPost::getRelatedPosts($id_product, $this->context->language->id);

        $this->context->smarty->assign([
            'related_posts' => $related_posts
        ]);

        return $this->display(__FILE__, 'views/templates/hook/product-tab-content.tpl');
    }





    /**
     * Display in top area
     */
    public function hookDisplayTop($params)
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        // Show blog link in top area if enabled
        if (Configuration::get('STBLOG_SHOW_TOP_LINK')) {
            $this->context->smarty->assign([
                'blog_url' => $this->getBlogUrl(),
                'blog_title' => Configuration::get('STBLOG_META_TITLE', 'Blog')
            ]);

            return $this->display(__FILE__, 'views/templates/hook/top.tpl');
        }

        return '';
    }

    /**
     * Display in footer
     */
    public function hookDisplayFooter($params)
    {
        if (!Configuration::get('STBLOG_LIVE_MODE')) {
            return '';
        }

        // Show latest posts in footer if enabled
        if (Configuration::get('STBLOG_SHOW_FOOTER_POSTS')) {
            $latest_posts = StBlogPost::getLatestPosts($this->context->language->id, 3, $this->context->shop->id);

            foreach ($latest_posts as &$post) {
                $post['url'] = $this->getPostUrl($post['id_stblog_post'], $post['link_rewrite']);
            }

            $this->context->smarty->assign([
                'footer_posts' => $latest_posts,
                'blog_url' => $this->getBlogUrl()
            ]);

            return $this->display(__FILE__, 'views/templates/hook/footer.tpl');
        }

        return '';
    }

    /**
     * Get blog URL
     */
    public function getBlogUrl()
    {
        return $this->context->link->getModuleLink('stsmartblog', 'list');
    }

    /**
     * Get post URL
     */
    public function getPostUrl($id_post, $link_rewrite = null)
    {
        return $this->context->link->getModuleLink('stsmartblog', 'post', [
            'id_post' => (int)$id_post,
            'post_rewrite' => $link_rewrite
        ]);
    }

    /**
     * Get category URL
     */
    public function getCategoryUrl($id_category, $link_rewrite = null)
    {
        return $this->context->link->getModuleLink('stsmartblog', 'category', [
            'id_category' => (int)$id_category,
            'category_rewrite' => $link_rewrite
        ]);
    }
}

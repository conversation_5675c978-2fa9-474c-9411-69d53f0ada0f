<?php
/**
 * ST Smart Blog - Admin Categories Controller
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

class AdminStBlogCategoriesController extends ModuleAdminController
{
    public function __construct()
    {
        $this->table = 'stblog_category';
        $this->className = 'StBlogCategory';
        $this->lang = true;
        $this->addRowAction('edit');
        $this->addRowAction('delete');
       /* $this->bulk_actions = array(
            'delete' => array(
                'text' => $this->l('Delete selected'),
                'confirm' => $this->l('Delete selected items?')
            )
        );*/

        $this->bootstrap = true;
        $this->context = Context::getContext();

        parent::__construct();

        $this->meta_title = $this->l('Blog Categories');

        $this->fields_list = array(
            'id_stblog_category' => array(
                'title' => $this->l('ID'),
                'align' => 'center',
                'class' => 'fixed-width-xs'
            ),
            'title' => array(
                'title' => $this->l('Title'),
                'width' => 'auto',
                'filter_key' => 'b!title'
            ),
            'description' => array(
                'title' => $this->l('Description'),
                'width' => 200,
                'maxlength' => 100,
                'orderby' => false,
                'search' => false,
                'callback' => 'getDescriptionClean'
            ),
            'parent_title' => array(
                'title' => $this->l('Parent Category'),
                'width' => 150,
                'orderby' => false,
                'search' => false
            ),
            'position' => array(
                'title' => $this->l('Position'),
                'width' => 70,
                'align' => 'center',
                'position' => 'position'
            ),
            'active' => array(
                'title' => $this->l('Status'),
                'align' => 'center',
                'active' => 'status',
                'type' => 'bool',
                'class' => 'fixed-width-sm'
            ),
            'date_add' => array(
                'title' => $this->l('Date'),
                'width' => 120,
                'type' => 'datetime',
                'align' => 'right'
            )
        );

        $this->_select = 'pc.title as parent_title';
        $this->_join = 'LEFT JOIN `'._DB_PREFIX_.'stblog_category_lang` pcl ON (pcl.id_stblog_category = a.id_parent AND pcl.id_lang = '.(int)$this->context->language->id.')
                        LEFT JOIN `'._DB_PREFIX_.'stblog_category_lang` pc ON (pc.id_stblog_category = a.id_parent AND pc.id_lang = '.(int)$this->context->language->id.')';
        $this->_orderBy = 'position';
        $this->_orderWay = 'ASC';
    }

    public function getDescriptionClean($description)
    {
        return Tools::truncateString(strip_tags($description), 100);
    }

    public function renderForm()
    {
        $categories = StBlogCategory::getCategories($this->context->language->id, Context::getContext()->shop->id, false);
        $categories_array = array(array('id_stblog_category' => 0, 'title' => $this->l('Root')));
        
        foreach ($categories as $category) {
            if ($category['id_stblog_category'] != Tools::getValue('id_stblog_category')) {
                $categories_array[] = array(
                    'id_stblog_category' => $category['id_stblog_category'],
                    'title' => str_repeat('--', $category['level']) . ' ' . $category['title']
                );
            }
        }

        $this->fields_form = array(
            'legend' => array(
                'title' => $this->l('Category'),
                'icon' => 'icon-folder'
            ),
            'input' => array(
                array(
                    'type' => 'text',
                    'label' => $this->l('Title'),
                    'name' => 'title',
                    'lang' => true,
                    'required' => true,
                    'col' => 6,
                    'hint' => $this->l('Invalid characters:').' &lt;&gt;;=#{}'
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Description'),
                    'name' => 'description',
                    'lang' => true,
                    'rows' => 5,
                    'col' => 6,
                    'hint' => $this->l('Category description')
                ),
                array(
                    'type' => 'select',
                    'label' => $this->l('Parent category'),
                    'name' => 'id_parent',
                    'options' => array(
                        'query' => $categories_array,
                        'id' => 'id_stblog_category',
                        'name' => 'title'
                    ),
                    'col' => 6
                ),
                array(
                    'type' => 'text',
                    'label' => $this->l('Meta title'),
                    'name' => 'meta_title',
                    'lang' => true,
                    'col' => 6,
                    'hint' => $this->l('Forbidden characters:').' &lt;&gt;;=#{}'
                ),
                array(
                    'type' => 'textarea',
                    'label' => $this->l('Meta description'),
                    'name' => 'meta_description',
                    'lang' => true,
                    'rows' => 3,
                    'col' => 6,
                    'hint' => $this->l('Meta description for SEO')
                ),
                array(
                    'type' => 'tags',
                    'label' => $this->l('Meta keywords'),
                    'name' => 'meta_keywords',
                    'lang' => true,
                    'col' => 6,
                    'hint' => $this->l('Meta keywords for SEO, separated by commas')
                ),
                array(
                    'type' => 'text',
                    'label' => $this->l('Friendly URL'),
                    'name' => 'link_rewrite',
                    'lang' => true,
                    'required' => true,
                    'col' => 6,
                    'hint' => $this->l('Only letters, numbers, underscore (_) and the minus (-) character are allowed.')
                ),
                array(
                    'type' => 'switch',
                    'label' => $this->l('Active'),
                    'name' => 'active',
                    'required' => false,
                    'is_bool' => true,
                    'values' => array(
                        array(
                            'id' => 'active_on',
                            'value' => 1,
                            'label' => $this->l('Enabled')
                        ),
                        array(
                            'id' => 'active_off',
                            'value' => 0,
                            'label' => $this->l('Disabled')
                        )
                    )
                )
            ),
            'submit' => array(
                'title' => $this->l('Save')
            )
        );

        if (!($category = $this->loadObject(true))) {
            return;
        }

        return parent::renderForm();
    }

    public function processSave()
    {
        $category = new StBlogCategory((int)Tools::getValue('id_stblog_category'));
        
        // Generate link_rewrite if empty
        foreach (Language::getLanguages(false) as $language) {
            $title = Tools::getValue('title_'.$language['id_lang']);
            $link_rewrite = Tools::getValue('link_rewrite_'.$language['id_lang']);
            
            if (empty($link_rewrite) && !empty($title)) {
                $_POST['link_rewrite_'.$language['id_lang']] = Tools::link_rewrite($title);
            }
        }

        return parent::processSave();
    }

    public function processPosition()
    {
        if (!Validate::isLoadedObject($object = new StBlogCategory((int)Tools::getValue('id_stblog_category')))) {
            $this->errors[] = Tools::displayError('An error occurred while updating the status for an object.').' <b>'.$this->table.'</b> '.Tools::displayError('(cannot load object)');
        } elseif (!$object->updatePosition((int)Tools::getValue('way'), (int)Tools::getValue('position'))) {
            $this->errors[] = Tools::displayError('Failed to update the position.');
        } else {
            Tools::redirectAdmin(self::$currentIndex.'&'.$this->table.'Orderby=position&'.$this->table.'Orderway=asc&conf=5&token='.Tools::getAdminTokenLite('AdminStBlogCategories'));
        }
    }

    public function ajaxProcessUpdatePositions()
    {
        $way = (int)Tools::getValue('way');
        $id_stblog_category = (int)Tools::getValue('id');
        $positions = Tools::getValue($this->table);

        foreach ($positions as $position => $value) {
            $pos = explode('_', $value);

            if (isset($pos[2]) && (int)$pos[2] === $id_stblog_category) {
                if ($category = new StBlogCategory((int)$pos[2])) {
                    if (isset($position) && $category->updatePosition($way, $position)) {
                        echo 'ok position '.(int)$position.' for category '.(int)$pos[2].'\r\n';
                    } else {
                        echo '{"hasError" : true, "errors" : "Can not update category '.(int)$id_stblog_category.' to position '.(int)$position.' "}';
                    }
                } else {
                    echo '{"hasError" : true, "errors" : "This category ('.(int)$id_stblog_category.') can t be loaded"}';
                }
                break;
            }
        }
    }
}

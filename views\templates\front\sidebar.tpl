{*
* ST Smart Blog - Sidebar Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{* Search Widget *}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Search Blog' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <form action="{$blog_url}" method="get" class="blog-search-form">
            <div class="input-group">
                <input type="text" name="search" class="form-control" placeholder="{l s='Search posts...' mod='stsmartblog'}" value="{if isset($search_query)}{$search_query|escape:'html':'UTF-8'}{/if}">
                <div class="input-group-append">
                    <button type="submit" class="btn btn-primary">
                        <i class="fa fa-search"></i>
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

{* Categories Widget *}
{if $categories && count($categories) > 0}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Categories' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <ul class="category-list">
            {foreach from=$categories item=category}
                <li>
                    <a href="{$category.url}" title="{$category.title|escape:'html':'UTF-8'}">
                        {$category.title}
                        <span class="post-count">({$category.post_count})</span>
                    </a>
                </li>
            {/foreach}
        </ul>
    </div>
</div>
{/if}

{* Featured Posts Widget *}
{if $featured_posts && count($featured_posts) > 0}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Featured Posts' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        {foreach from=$featured_posts item=post}
            <div class="featured-post-item">
                <h4>
                    <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                        {$post.title|truncate:50:'...'}
                    </a>
                </h4>
                <div class="post-date">
                    <i class="fa fa-calendar"></i>
                    {$post.date_formatted}
                </div>
            </div>
        {/foreach}
    </div>
</div>
{/if}

{* Recent Posts Widget *}
{if $recent_posts && count($recent_posts) > 0}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Recent Posts' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        {foreach from=$recent_posts item=post}
            <div class="recent-post-item">
                <h4>
                    <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                        {$post.title|truncate:45:'...'}
                    </a>
                </h4>
                <div class="post-meta">
                    <span class="post-date">
                        <i class="fa fa-calendar"></i>
                        {$post.date_formatted}
                    </span>
                    <span class="post-views">
                        <i class="fa fa-eye"></i>
                        {$post.views}
                    </span>
                </div>
            </div>
        {/foreach}
    </div>
</div>
{/if}

{* Popular Tags Widget *}
{if $popular_tags && count($popular_tags) > 0}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Popular Tags' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <div class="tag-cloud">
            {foreach from=$popular_tags item=tag}
                <a href="{$tag.url}" class="tag-item" style="font-size: {$tag.weight}px;" title="{$tag.name|escape:'html':'UTF-8'}">
                    {$tag.name}
                </a>
            {/foreach}
        </div>
    </div>
</div>
{/if}

{* Archive Widget *}
{if $archive_months && count($archive_months) > 0}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Archive' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <ul class="archive-list">
            {foreach from=$archive_months item=month}
                <li>
                    <a href="{$month.url}" title="{$month.label|escape:'html':'UTF-8'}">
                        {$month.label}
                        <span class="post-count">({$month.post_count})</span>
                    </a>
                </li>
            {/foreach}
        </ul>
    </div>
</div>
{/if}

{* Newsletter Widget *}
{if $show_newsletter}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Newsletter' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <p>{l s='Subscribe to our newsletter to get the latest updates.' mod='stsmartblog'}</p>
        <form action="{$newsletter_url}" method="post" class="newsletter-form">
            <div class="form-group">
                <input type="email" name="email" class="form-control" placeholder="{l s='Your email address' mod='stsmartblog'}" required>
            </div>
            <button type="submit" class="btn btn-primary btn-block">
                {l s='Subscribe' mod='stsmartblog'}
            </button>
        </form>
    </div>
</div>
{/if}

{* Social Links Widget *}
{if $social_links && count($social_links) > 0}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Follow Us' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        <div class="social-links">
            {foreach from=$social_links item=link}
                <a href="{$link.url}" target="_blank" class="social-link social-{$link.type}" title="{$link.title|escape:'html':'UTF-8'}">
                    <i class="fa fa-{$link.icon}"></i>
                </a>
            {/foreach}
        </div>
    </div>
</div>
{/if}

{* Custom Widget - Latest Comments *}
{if $latest_comments && count($latest_comments) > 0}
<div class="sidebar-widget">
    <h3 class="widget-title">
        {l s='Latest Comments' mod='stsmartblog'}
    </h3>
    <div class="widget-content">
        {foreach from=$latest_comments item=comment}
            <div class="latest-comment-item">
                <div class="comment-author">
                    <strong>{$comment.author_name}</strong>
                </div>
                <div class="comment-excerpt">
                    {$comment.content|strip_tags|truncate:60:'...'}
                </div>
                <div class="comment-post">
                    {l s='on' mod='stsmartblog'} 
                    <a href="{$comment.post_url}" title="{$comment.post_title|escape:'html':'UTF-8'}">
                        {$comment.post_title|truncate:30:'...'}
                    </a>
                </div>
                <div class="comment-date">
                    <i class="fa fa-clock-o"></i>
                    {$comment.date_formatted}
                </div>
            </div>
        {/foreach}
    </div>
</div>
{/if}

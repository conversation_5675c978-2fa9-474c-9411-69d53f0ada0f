{*
* ST Smart Blog - Single Post Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{extends file='page.tpl'}

{block name='page_title'}
    {$post.title}
{/block}

{block name='page_content'}
<div class="stblog-wrapper">
    <div class="row">
        <div class="col-lg-8 col-md-12">
            <article class="stblog-post-single">
                <header class="post-header">
                    <h1 class="post-title">{$post.title}</h1>
                    
                    <div class="post-meta">
                        <span class="post-date">
                            <i class="fa fa-calendar"></i>
                            {$post.date_formatted}
                        </span>
                        <span class="post-author">
                            <i class="fa fa-user"></i>
                            {l s='By' mod='stsmartblog'} {$post.author_name|default:'Admin'}
                        </span>
                        <span class="post-views">
                            <i class="fa fa-eye"></i>
                            {$post.views} {l s='views' mod='stsmartblog'}
                        </span>
                        {if $post.category_title}
                        <span class="post-category">
                            <i class="fa fa-folder"></i>
                            <a href="{$post.category_url}" title="{$post.category_title|escape:'html':'UTF-8'}">
                                {$post.category_title}
                            </a>
                        </span>
                        {/if}
                    </div>
                    
                    {if $post.content_short}
                    <div class="post-excerpt">
                        {$post.content_short nofilter}
                    </div>
                    {/if}
                </header>
                
                <div class="post-content">
                    {$post.content nofilter}
                </div>
                
                {if $post.tags && count($post.tags) > 0}
                <div class="post-tags">
                    <h4>{l s='Tags:' mod='stsmartblog'}</h4>
                    {foreach from=$post.tags item=tag}
                        <a href="{$tag.url}" class="tag-item" title="{$tag.name|escape:'html':'UTF-8'}">
                            {$tag.name}
                        </a>
                    {/foreach}
                </div>
                {/if}
                
                <div class="social-share">
                    <h4 class="social-share-title">{l s='Share this post:' mod='stsmartblog'}</h4>
                    <div class="social-share-buttons">
                        <a href="https://www.facebook.com/sharer/sharer.php?u={$post.full_url|urlencode}" 
                           class="social-share-btn social-facebook" target="_blank" rel="noopener">
                            <i class="fa fa-facebook"></i> Facebook
                        </a>
                        <a href="https://twitter.com/intent/tweet?url={$post.full_url|urlencode}&text={$post.title|urlencode}" 
                           class="social-share-btn social-twitter" target="_blank" rel="noopener">
                            <i class="fa fa-twitter"></i> Twitter
                        </a>
                        <a href="https://www.linkedin.com/sharing/share-offsite/?url={$post.full_url|urlencode}" 
                           class="social-share-btn social-linkedin" target="_blank" rel="noopener">
                            <i class="fa fa-linkedin"></i> LinkedIn
                        </a>
                        <a href="https://pinterest.com/pin/create/button/?url={$post.full_url|urlencode}&description={$post.title|urlencode}" 
                           class="social-share-btn social-pinterest" target="_blank" rel="noopener">
                            <i class="fa fa-pinterest"></i> Pinterest
                        </a>
                        <a href="https://wa.me/?text={$post.title|urlencode}%20{$post.full_url|urlencode}" 
                           class="social-share-btn social-whatsapp" target="_blank" rel="noopener">
                            <i class="fa fa-whatsapp"></i> WhatsApp
                        </a>
                        <a href="#" class="social-share-btn copy-link-btn">
                            <i class="fa fa-link"></i> {l s='Copy Link' mod='stsmartblog'}
                        </a>
                    </div>
                </div>
                
                <div class="post-navigation">
                    <div class="row">
                        {if $previous_post}
                        <div class="col-md-6">
                            <div class="nav-post nav-previous">
                                <span class="nav-label">{l s='Previous Post' mod='stsmartblog'}</span>
                                <h5>
                                    <a href="{$previous_post.url}" title="{$previous_post.title|escape:'html':'UTF-8'}">
                                        <i class="fa fa-arrow-left"></i>
                                        {$previous_post.title|truncate:40:'...'}
                                    </a>
                                </h5>
                            </div>
                        </div>
                        {/if}
                        
                        {if $next_post}
                        <div class="col-md-6">
                            <div class="nav-post nav-next">
                                <span class="nav-label">{l s='Next Post' mod='stsmartblog'}</span>
                                <h5>
                                    <a href="{$next_post.url}" title="{$next_post.title|escape:'html':'UTF-8'}">
                                        {$next_post.title|truncate:40:'...'}
                                        <i class="fa fa-arrow-right"></i>
                                    </a>
                                </h5>
                            </div>
                        </div>
                        {/if}
                    </div>
                </div>
            </article>
            
            {if $related_posts && count($related_posts) > 0}
            <section class="related-posts">
                <h3 class="related-posts-title">{l s='Related Posts' mod='stsmartblog'}</h3>
                <div class="row">
                    {foreach from=$related_posts item=related_post}
                        <div class="col-md-6">
                            <article class="related-post-item">
                                <h4>
                                    <a href="{$related_post.url}" title="{$related_post.title|escape:'html':'UTF-8'}">
                                        {$related_post.title|truncate:50:'...'}
                                    </a>
                                </h4>
                                <div class="post-meta">
                                    <span class="post-date">
                                        <i class="fa fa-calendar"></i>
                                        {$related_post.date_formatted}
                                    </span>
                                    <span class="post-views">
                                        <i class="fa fa-eye"></i>
                                        {$related_post.views}
                                    </span>
                                </div>
                                <p>{$related_post.content_short|strip_tags|truncate:100:'...'}</p>
                            </article>
                        </div>
                    {/foreach}
                </div>
            </section>
            {/if}
            
            {if $comments_enabled}
            <section class="stblog-comments">
                <h3 class="comments-title">
                    {l s='Comments' mod='stsmartblog'} ({$comments_count})
                </h3>
                
                {if $comments && count($comments) > 0}
                    <div class="comments-list">
                        {foreach from=$comments item=comment}
                            <div class="comment-item" id="comment-{$comment.id_stblog_comment}">
                                <div class="comment-header">
                                    <span class="comment-author">{$comment.author_name}</span>
                                    <span class="comment-date">{$comment.date_formatted}</span>
                                    {if $comment.id_parent == 0}
                                        <a href="#" class="comment-reply-btn" data-comment-id="{$comment.id_stblog_comment}">
                                            {l s='Reply' mod='stsmartblog'}
                                        </a>
                                    {/if}
                                </div>
                                <div class="comment-content">
                                    {$comment.content|nl2br nofilter}
                                </div>
                                
                                {if $comment.replies && count($comment.replies) > 0}
                                    <div class="comment-replies">
                                        {foreach from=$comment.replies item=reply}
                                            <div class="comment-item comment-reply">
                                                <div class="comment-header">
                                                    <span class="comment-author">{$reply.author_name}</span>
                                                    <span class="comment-date">{$reply.date_formatted}</span>
                                                </div>
                                                <div class="comment-content">
                                                    {$reply.content|nl2br nofilter}
                                                </div>
                                            </div>
                                        {/foreach}
                                    </div>
                                {/if}
                            </div>
                        {/foreach}
                    </div>
                {else}
                    <p class="no-comments">{l s='No comments yet. Be the first to comment!' mod='stsmartblog'}</p>
                {/if}
                
                <div class="comment-form-container">
                    <form action="{$comment_form_action}" method="post" class="comment-form" id="stblog-comment-form">
                        <h4 class="form-title">{l s='Leave a comment' mod='stsmartblog'}</h4>
                        
                        <input type="hidden" name="id_stblog_post" value="{$post.id_stblog_post}">
                        <input type="hidden" name="id_parent" value="0">
                        <input type="hidden" name="token" value="{$comment_token}">
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="author_name">{l s='Name' mod='stsmartblog'} *</label>
                                    <input type="text" name="author_name" id="author_name" class="form-control" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="author_email">{l s='Email' mod='stsmartblog'} *</label>
                                    <input type="email" name="author_email" id="author_email" class="form-control" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="content">{l s='Comment' mod='stsmartblog'} *</label>
                            <textarea name="content" id="content" class="form-control" rows="5" required></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">
                                {l s='Post Comment' mod='stsmartblog'}
                            </button>
                        </div>
                    </form>
                </div>
            </section>
            {/if}
        </div>
        
        <div class="col-lg-4 col-md-12">
            <aside class="stblog-sidebar">
                {include file='module:stsmartblog/views/templates/front/sidebar.tpl'}
            </aside>
        </div>
    </div>
</div>

{if $blog_structured_data}
<script type="application/ld+json">
{$blog_structured_data nofilter}
</script>
{/if}
{/block}

{extends file='page.tpl'}

{block name='page_title'}
    <h1>{$blog_title}</h1>
{/block}

{block name='page_content'}
    <div class="stblog-wrapper">
        <div class="row">
            <div class="col-lg-9 col-md-8">
                <div class="stblog-posts">
                    {if $posts && count($posts) > 0}
                        {foreach from=$posts item=post}
                            <article class="stblog-post-item">
                                <div class="post-header">
                                    <h2 class="post-title">
                                        <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">{$post.title}</a>
                                    </h2>
                                    <div class="post-meta">
                                        <span class="post-date">
                                            <i class="fa fa-calendar"></i>
                                            {$post.date_formatted}
                                        </span>
                                        <span class="post-category">
                                            <i class="fa fa-folder"></i>
                                            <a href="{$post.category_url}">{$post.category->name}</a>
                                        </span>
                                        <span class="post-views">
                                            <i class="fa fa-eye"></i>
                                            {$post.views} {l s='views' mod='stsmartblog'}
                                        </span>
                                        {if $post.comments_count > 0}
                                            <span class="post-comments">
                                                <i class="fa fa-comments"></i>
                                                {$post.comments_count} {l s='comments' mod='stsmartblog'}
                                            </span>
                                        {/if}
                                    </div>
                                </div>
                                
                                <div class="post-content">
                                    {if $post.content_short}
                                        <p>{$post.content_short|strip_tags|truncate:200:'...'}</p>
                                    {else}
                                        <p>{$post.content|strip_tags|truncate:200:'...'}</p>
                                    {/if}
                                </div>
                                
                                <div class="post-footer">
                                    <a href="{$post.url}" class="btn btn-primary">
                                        {l s='Read more' mod='stsmartblog'}
                                    </a>
                                </div>
                            </article>
                        {/foreach}
                        
                        {* Pagination *}
                        {if $pagination && count($pagination) > 1}
                            <nav class="stblog-pagination">
                                <ul class="pagination">
                                    {foreach from=$pagination item=page_item}
                                        <li class="page-item {if $page_item.current}active{/if}">
                                            <a class="page-link" href="{$page_item.url}">{$page_item.page}</a>
                                        </li>
                                    {/foreach}
                                </ul>
                            </nav>
                        {/if}
                    {else}
                        <div class="alert alert-info">
                            {l s='No blog posts found.' mod='stsmartblog'}
                        </div>
                    {/if}
                </div>
            </div>
            
            <div class="col-lg-3 col-md-4">
                <aside class="stblog-sidebar">
                    {* Featured Posts *}
                    {if $featured_posts && count($featured_posts) > 0}
                        <div class="sidebar-widget featured-posts">
                            <h3 class="widget-title">{l s='Featured Posts' mod='stsmartblog'}</h3>
                            <div class="widget-content">
                                {foreach from=$featured_posts item=featured_post}
                                    <div class="featured-post-item">
                                        <h4>
                                            <a href="{$featured_post.url}" title="{$featured_post.title|escape:'html':'UTF-8'}">
                                                {$featured_post.title|truncate:50:'...'}
                                            </a>
                                        </h4>
                                        <div class="post-date">
                                            <small>{$featured_post.date_formatted}</small>
                                        </div>
                                    </div>
                                {/foreach}
                            </div>
                        </div>
                    {/if}
                    
                    {* Categories *}
                    {if $categories && count($categories) > 0}
                        <div class="sidebar-widget categories">
                            <h3 class="widget-title">{l s='Categories' mod='stsmartblog'}</h3>
                            <div class="widget-content">
                                <ul class="category-list">
                                    {foreach from=$categories item=category}
                                        <li>
                                            <a href="{$category.url}" title="{$category.name|escape:'html':'UTF-8'}">
                                                {$category.name}
                                                <span class="post-count">({$category.posts_count})</span>
                                            </a>
                                        </li>
                                    {/foreach}
                                </ul>
                            </div>
                        </div>
                    {/if}
                </aside>
            </div>
        </div>
    </div>
{/block}

{block name='page_footer'}
    <style>
        .stblog-wrapper {
            margin: 20px 0;
        }
        
        .stblog-post-item {
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #eee;
        }
        
        .post-title {
            margin-bottom: 10px;
        }
        
        .post-title a {
            color: #333;
            text-decoration: none;
        }
        
        .post-title a:hover {
            color: #007bff;
        }
        
        .post-meta {
            margin-bottom: 15px;
            color: #666;
            font-size: 14px;
        }
        
        .post-meta span {
            margin-right: 15px;
        }
        
        .post-meta i {
            margin-right: 5px;
        }
        
        .post-content {
            margin-bottom: 15px;
            line-height: 1.6;
        }
        
        .stblog-sidebar {
            padding-left: 20px;
        }
        
        .sidebar-widget {
            margin-bottom: 30px;
            background: #f8f9fa;
            padding: 20px;
            border-radius: 5px;
        }
        
        .widget-title {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
            font-size: 18px;
        }
        
        .category-list {
            list-style: none;
            padding: 0;
        }
        
        .category-list li {
            margin-bottom: 8px;
        }
        
        .category-list a {
            color: #333;
            text-decoration: none;
        }
        
        .category-list a:hover {
            color: #007bff;
        }
        
        .post-count {
            color: #666;
            font-size: 12px;
        }
        
        .featured-post-item {
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #ddd;
        }
        
        .featured-post-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .featured-post-item h4 {
            margin-bottom: 5px;
            font-size: 14px;
        }
        
        .featured-post-item a {
            color: #333;
            text-decoration: none;
        }
        
        .featured-post-item a:hover {
            color: #007bff;
        }
        
        .stblog-pagination {
            margin-top: 30px;
            text-align: center;
        }
        
        @media (max-width: 768px) {
            .stblog-sidebar {
                padding-left: 0;
                margin-top: 30px;
            }
        }
    </style>
{/block}

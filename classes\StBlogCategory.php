<?php
/**
 * ST Smart Blog - Blog Category Class
 * 
 * <AUTHOR>
 * @copyright 2024 ST-themes
 * @license   Academic Free License (AFL 3.0)
 */

if (!defined('_PS_VERSION_')) {
    exit;
}

class StBlogCategory extends ObjectModel
{
    /** @var int */
    public $id_stblog_category;

    /** @var int */
    public $id_parent;

    /** @var int */
    public $position;

    /** @var bool */
    public $active;

    /** @var string */
    public $date_add;

    /** @var string */
    public $date_upd;

    /** @var string */
    public $name;

    /** @var string */
    public $description;

    /** @var string */
    public $meta_title;

    /** @var string */
    public $meta_description;

    /** @var string */
    public $meta_keywords;

    /** @var string */
    public $link_rewrite;

    /**
     * @see ObjectModel::$definition
     */
    public static $definition = [
        'table' => 'stblog_category',
        'primary' => 'id_stblog_category',
        'multilang' => true,
        'multilang_shop' => true,
        'fields' => [
            'id_parent' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedId'],
            'position' => ['type' => self::TYPE_INT, 'validate' => 'isUnsignedInt'],
            'active' => ['type' => self::TYPE_BOOL, 'validate' => 'isBool'],
            'date_add' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],
            'date_upd' => ['type' => self::TYPE_DATE, 'validate' => 'isDate'],

            // Lang fields
            'name' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'required' => true, 'size' => 255],
            'description' => ['type' => self::TYPE_HTML, 'lang' => true, 'validate' => 'isCleanHtml'],
            'meta_title' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 255],
            'meta_description' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 512],
            'meta_keywords' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isGenericName', 'size' => 255],
            'link_rewrite' => ['type' => self::TYPE_STRING, 'lang' => true, 'validate' => 'isLinkRewrite', 'required' => true, 'size' => 128],
        ],
    ];

    public function __construct($id = null, $id_lang = null, $id_shop = null)
    {
        parent::__construct($id, $id_lang, $id_shop);
    }

    public function add($autodate = true, $null_values = false)
    {
        if (empty($this->position)) {
            $this->position = $this->getHighestPosition() + 1;
        }

        return parent::add($autodate, $null_values);
    }

    public function delete()
    {
        // Move posts to default category before deleting
        $default_category = 1; // Root category
        Db::getInstance()->update(
            'stblog_post',
            ['id_category' => (int)$default_category],
            'id_category = ' . (int)$this->id
        );

        return parent::delete();
    }

    /**
     * Get highest position
     */
    public function getHighestPosition()
    {
        $sql = 'SELECT MAX(position) FROM `' . _DB_PREFIX_ . 'stblog_category`';
        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Get all categories
     */
    public static function getCategories($id_lang, $active = true, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT c.*, cl.name, cl.description, cl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_category` c
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_category_lang` cl ON (c.id_stblog_category = cl.id_stblog_category)
                WHERE cl.id_lang = ' . (int)$id_lang . '
                AND cl.id_shop = ' . (int)$id_shop;

        if ($active) {
            $sql .= ' AND c.active = 1';
        }

        $sql .= ' ORDER BY c.position ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Get category tree
     */
    public static function getCategoryTree($id_lang, $id_parent = 0, $active = true, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT c.*, cl.name, cl.description, cl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_category` c
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_category_lang` cl ON (c.id_stblog_category = cl.id_stblog_category)
                WHERE c.id_parent = ' . (int)$id_parent . '
                AND cl.id_lang = ' . (int)$id_lang . '
                AND cl.id_shop = ' . (int)$id_shop;

        if ($active) {
            $sql .= ' AND c.active = 1';
        }

        $sql .= ' ORDER BY c.position ASC';

        $categories = Db::getInstance()->executeS($sql);

        foreach ($categories as &$category) {
            $category['children'] = self::getCategoryTree($id_lang, $category['id_stblog_category'], $active, $id_shop);
            $category['posts_count'] = self::getPostsCount($category['id_stblog_category']);
        }

        return $categories;
    }

    /**
     * Get category by link rewrite
     */
    public static function getCategoryByRewrite($link_rewrite, $id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT c.id_stblog_category
                FROM `' . _DB_PREFIX_ . 'stblog_category` c
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_category_lang` cl ON (c.id_stblog_category = cl.id_stblog_category)
                WHERE c.active = 1 
                AND cl.link_rewrite = "' . pSQL($link_rewrite) . '"
                AND cl.id_lang = ' . (int)$id_lang . '
                AND cl.id_shop = ' . (int)$id_shop;

        $id_category = Db::getInstance()->getValue($sql);
        
        if ($id_category) {
            return new StBlogCategory($id_category, $id_lang, $id_shop);
        }
        
        return false;
    }

    /**
     * Get posts count for category
     */
    public static function getPostsCount($id_category)
    {
        $sql = 'SELECT COUNT(*)
                FROM `' . _DB_PREFIX_ . 'stblog_post`
                WHERE id_category = ' . (int)$id_category . '
                AND active = 1
                AND date_publish <= NOW()';

        return (int)Db::getInstance()->getValue($sql);
    }

    /**
     * Get category breadcrumb
     */
    public function getBreadcrumb($id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $breadcrumb = [];
        $current_category = $this;

        while ($current_category && $current_category->id > 0) {
            $breadcrumb[] = [
                'id' => $current_category->id,
                'name' => $current_category->name,
                'link_rewrite' => $current_category->link_rewrite
            ];

            if ($current_category->id_parent > 0) {
                $current_category = new StBlogCategory($current_category->id_parent, $id_lang, $id_shop);
            } else {
                break;
            }
        }

        return array_reverse($breadcrumb);
    }

    /**
     * Check if category has children
     */
    public function hasChildren()
    {
        $sql = 'SELECT COUNT(*)
                FROM `' . _DB_PREFIX_ . 'stblog_category`
                WHERE id_parent = ' . (int)$this->id . '
                AND active = 1';

        return (int)Db::getInstance()->getValue($sql) > 0;
    }

    /**
     * Get children categories
     */
    public function getChildren($id_lang, $id_shop = null)
    {
        if (!$id_shop) {
            $id_shop = Context::getContext()->shop->id;
        }

        $sql = 'SELECT c.*, cl.name, cl.description, cl.link_rewrite
                FROM `' . _DB_PREFIX_ . 'stblog_category` c
                LEFT JOIN `' . _DB_PREFIX_ . 'stblog_category_lang` cl ON (c.id_stblog_category = cl.id_stblog_category)
                WHERE c.id_parent = ' . (int)$this->id . '
                AND c.active = 1
                AND cl.id_lang = ' . (int)$id_lang . '
                AND cl.id_shop = ' . (int)$id_shop . '
                ORDER BY c.position ASC';

        return Db::getInstance()->executeS($sql);
    }

    /**
     * Move category to new parent
     */
    public function moveToParent($id_new_parent)
    {
        // Prevent moving to itself or its children
        if ($id_new_parent == $this->id || $this->isChild($id_new_parent)) {
            return false;
        }

        $this->id_parent = (int)$id_new_parent;
        return $this->update();
    }

    /**
     * Check if given category is a child of current category
     */
    private function isChild($id_category)
    {
        $sql = 'SELECT id_stblog_category
                FROM `' . _DB_PREFIX_ . 'stblog_category`
                WHERE id_parent = ' . (int)$this->id;

        $children = Db::getInstance()->executeS($sql);

        foreach ($children as $child) {
            if ($child['id_stblog_category'] == $id_category) {
                return true;
            }
            
            $child_category = new StBlogCategory($child['id_stblog_category']);
            if ($child_category->isChild($id_category)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Update positions
     */
    public static function updatePosition($id_category, $way, $position)
    {
        $sql = 'SELECT c.id_stblog_category, c.position
                FROM `' . _DB_PREFIX_ . 'stblog_category` c
                WHERE c.id_stblog_category = ' . (int)$id_category;

        $category = Db::getInstance()->getRow($sql);

        if (!$category) {
            return false;
        }

        $moved_category = new StBlogCategory($category['id_stblog_category']);

        if ($way == 'up') {
            $sql = 'SELECT c.id_stblog_category, c.position
                    FROM `' . _DB_PREFIX_ . 'stblog_category` c
                    WHERE c.position < ' . (int)$category['position'] . '
                    ORDER BY c.position DESC
                    LIMIT 1';
        } else {
            $sql = 'SELECT c.id_stblog_category, c.position
                    FROM `' . _DB_PREFIX_ . 'stblog_category` c
                    WHERE c.position > ' . (int)$category['position'] . '
                    ORDER BY c.position ASC
                    LIMIT 1';
        }

        $replaced_category_data = Db::getInstance()->getRow($sql);

        if (!$replaced_category_data) {
            return false;
        }

        $replaced_category = new StBlogCategory($replaced_category_data['id_stblog_category']);

        $moved_category->position = $replaced_category_data['position'];
        $replaced_category->position = $category['position'];

        return $moved_category->update() && $replaced_category->update();
    }
}

{*
* ST Smart Blog - Footer Hook Template
* 
* <AUTHOR>
* @copyright 2024 ST-themes
* @license   Academic Free License (AFL 3.0)
*}

{if $footer_posts && count($footer_posts) > 0}
<div class="stblog-footer-section">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h3 class="footer-section-title">
                    {l s='Latest from Our Blog' mod='stsmartblog'}
                </h3>
            </div>
        </div>
        
        <div class="row">
            {foreach from=$footer_posts item=post}
                <div class="col-lg-4 col-md-6 col-sm-12">
                    <article class="footer-post-item">
                        <div class="post-header">
                            <h4 class="post-title">
                                <a href="{$post.url}" title="{$post.title|escape:'html':'UTF-8'}">
                                    {$post.title|truncate:50:'...'}
                                </a>
                            </h4>
                            <div class="post-meta">
                                <span class="post-date">
                                    <i class="fa fa-calendar"></i>
                                    {$post.date_formatted}
                                </span>
                                <span class="post-views">
                                    <i class="fa fa-eye"></i>
                                    {$post.views}
                                </span>
                            </div>
                        </div>
                        
                        <div class="post-content">
                            {if $post.content_short}
                                <p>{$post.content_short|strip_tags|truncate:80:'...'}</p>
                            {else}
                                <p>{$post.content|strip_tags|truncate:80:'...'}</p>
                            {/if}
                        </div>
                        
                        <div class="post-footer">
                            <a href="{$post.url}" class="read-more-link">
                                {l s='Read More' mod='stsmartblog'}
                                <i class="fa fa-arrow-right"></i>
                            </a>
                        </div>
                    </article>
                </div>
            {/foreach}
        </div>
        
        <div class="row">
            <div class="col-12 text-center">
                <a href="{$blog_url}" class="btn btn-outline-light">
                    {l s='Visit Our Blog' mod='stsmartblog'}
                </a>
            </div>
        </div>
    </div>
</div>

<style>
.stblog-footer-section {
    background: #2c3e50;
    color: #ecf0f1;
    padding: 40px 0 30px;
    margin-top: 30px;
    border-top: 3px solid #007bff;
}

.footer-section-title {
    text-align: center;
    margin-bottom: 30px;
    font-size: 24px;
    font-weight: 600;
    color: #ecf0f1;
    position: relative;
}

.footer-section-title:after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 2px;
    background: #007bff;
}

.footer-post-item {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    transition: transform 0.3s ease, background-color 0.3s ease;
    height: 100%;
    display: flex;
    flex-direction: column;
}

.footer-post-item:hover {
    transform: translateY(-3px);
    background: rgba(255, 255, 255, 0.08);
}

.footer-post-item .post-title {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
}

.footer-post-item .post-title a {
    color: #ecf0f1;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-post-item .post-title a:hover {
    color: #3498db;
}

.footer-post-item .post-meta {
    margin-bottom: 15px;
    color: #bdc3c7;
    font-size: 13px;
}

.footer-post-item .post-meta span {
    margin-right: 15px;
}

.footer-post-item .post-meta i {
    margin-right: 5px;
}

.footer-post-item .post-content {
    flex-grow: 1;
    margin-bottom: 15px;
    line-height: 1.6;
    color: #bdc3c7;
    font-size: 14px;
}

.footer-post-item .post-footer {
    margin-top: auto;
}

.footer-post-item .read-more-link {
    color: #3498db;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.3s ease;
}

.footer-post-item .read-more-link:hover {
    color: #2980b9;
    text-decoration: none;
}

.footer-post-item .read-more-link i {
    margin-left: 5px;
    transition: transform 0.3s ease;
}

.footer-post-item .read-more-link:hover i {
    transform: translateX(3px);
}

.btn-outline-light {
    border-color: #ecf0f1;
    color: #ecf0f1;
    margin-top: 20px;
    padding: 10px 30px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-outline-light:hover {
    background-color: #ecf0f1;
    color: #2c3e50;
    border-color: #ecf0f1;
}

@media (max-width: 768px) {
    .stblog-footer-section {
        padding: 30px 0 20px;
    }
    
    .footer-section-title {
        font-size: 20px;
        margin-bottom: 25px;
    }
    
    .footer-post-item {
        padding: 15px;
        margin-bottom: 15px;
    }
    
    .footer-post-item .post-meta span {
        display: block;
        margin-bottom: 5px;
        margin-right: 0;
    }
}

@media (max-width: 576px) {
    .footer-post-item .post-title {
        font-size: 15px;
    }
    
    .footer-post-item .post-content {
        font-size: 13px;
    }
    
    .btn-outline-light {
        padding: 8px 20px;
        font-size: 14px;
    }
}
</style>
{/if}
